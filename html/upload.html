<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/html">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>实时TTS缓存更新工具</title>
    <style>
        .button {
            background-color: #489EFF;
            padding: 10px 30px 10px 30px;
            border-radius: 5px;
            color: white;
        }

        .audio_file_name {
            padding-left: 50px;
        }

        input[type=file] {
            display: none;
        }

        #submit {
            position: fixed;
            left: 50%;
        }
    </style>
</head>
<body>

<h3>实时TTS缓存更新工具-Llamabot</h3>

<form id="my-form" action="/cache/batch_add" method="post" enctype="multipart/form-data" >
    <label style="padding: 40px 0 40px 0">新增缓存更新区域：</label>
    <label style="padding-right: 20px"><input type="radio" name="region" value="all" checked onclick="disable_region('none')">全部区域</label>
    <label style="padding-right: 40px"><input type="radio" name="region" value="custom" onclick="disable_region('inline')" >部分区域</label>


    <span id="custom_region" style="display: none">

    {{ range $key, $value := .Address }}
    <label><input type="checkbox" name="custom_region" value="{{ $key }}">{{ $key }}</label>
    {{ end }}

    </span>

    <br />
    <br />


    <input id="audio_update" type="file" name="audio_file" accept=".zip" onchange="showFilename('audio_update', 'audio_file_name')">
    <label for="audio_update" class="button">上传音频</label>

    <label id="audio_file_name" class="audio_file_name"></label>
    <br />
    <br />
    <br />

    <input id="csv_update" type="file" name="csv_file" accept=".csv" onchange="showFilename('csv_update', 'csv_file_name')">
    <label for="csv_update" class="button">上传文件</label>
    <label id="csv_file_name" class="audio_file_name"></label>
    <br />
    <br />
    <label><a href="/cache/cache_file" download="cache_file.csv">下载模板</a> </label>

    <br />
    <br />
    <div id="response" style="color: red"></div>
    <br />
    <br />
    <br />
    <button type="submit" class="button" id="submit">确认更新</button>
</form>


<script>
    function showFilename(audio_id, label_id) {
        document.getElementById(label_id).innerHTML = document.getElementById(audio_id).files[0].name;
    }

    function disable_region(display) {
        document.getElementById("custom_region").style.display = display
    }

    const form = document.getElementById('my-form');
    const responseContainer = document.getElementById('response');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        responseContainer.innerHTML = "上传中...";
        const audio_file = document.getElementById("audio_update").files;
        if (audio_file.length === 0){
            responseContainer.innerHTML = "audio file must be upload";
            return false
        }

        const csv_file = document.getElementById("csv_update").files;
        if (csv_file.length === 0){
            responseContainer.innerHTML = "csv file must be upload";
            return false
        }

        const xhr = new XMLHttpRequest();
        xhr.open('POST', form.action);
        xhr.onload = function() {
            if (xhr.status === 200) {
                var response = JSON.parse(xhr.response);
                if (response.code === 200){
                    responseContainer.innerHTML = "上传成功"
                } else {
                    responseContainer.innerHTML = response.status;
                }
            } else {
                responseContainer.innerHTML = xhr.responseText
            }
        };
        xhr.send(new FormData(form));
    });
</script>


</body>
</html>