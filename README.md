### 代码仓库

https://gitlab.airudder.com/paas/tts-service

### 项目介绍

这是老项目 [tts-service](https://gitlab.airudder.com/airudder-production/tts) 的 **Golang** 重置版本，目的是支持更高的并发以及后续支持流式音频

**Python**版本实现的系统无法支持GRPC更高的并发，因此需要改成 **Golang** 来支持更高的并发，以及更完善的 **GRPC** 支持

### 架构选型

- **Golang**版本: 1.19.2
- Http服务: gin
- 配置: apollo + viper
- 日志库: zap

### 功能列表

- [X] 基本框架搭建
- [X] 自研 TTS 支持
- [X] Google TTS 支持
- [X] Microsoft TTS 支持
- [X] GRPC 实时TTS支持
- [X] 自研 TTS v1 支持（使用转发）
- [X] 自研 TTS 扩缩容支持
- [X] 音频流支持