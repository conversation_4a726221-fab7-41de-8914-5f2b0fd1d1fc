package pck2wav

import (
	"fmt"
	"os"
	"testing"
	"tts-service/internal/pkg/utils"
)

func TestPcm2Wav(t *testing.T) {
	content, _ := os.ReadFile("/Users/<USER>/Desktop/output.wav")
	z, _ := Pcm2Wav(content, 8000)
	file, _ := os.OpenFile("/Users/<USER>/Desktop/audio.wav", os.O_WRONLY|os.O_CREATE, 0666)

	defer file.Close()
	_, _ = file.Write(z)

	_ = file.Sync()
}

func TestMd5Join(t *testing.T) {
	key := "realtime_tts_397c00fa-c355-4aae-bc38-c583c2ffd845.wav+<speak><say-as interpret-as='name' fields=\"nickname\">yusuf</say-as><break time='100ms'/></speak>+1fd7f146-7099-4b8c-8207-37294ebe0b8e.wav_[<speak><say-as interpret-as='name' fields=\"nickname\">yusuf</say-as><break time='100ms'/></speak>]_airudder__v2_id-ID-syifa_tts3-0-2-id-id-7voices-fserhi-outbound-realtime_0.800000_0.000000__true_v6"
	specialKey := "${gender}_introduction1_1+<speak><say-as interpret-as='name' fields=\"nickname\">${borrower_name}</say-as><break time='100ms'/></speak>+PO_Cln_ID_BA_Syi_Mobi_DM2_v2_Introduction1_3.mp3"
	x := utils.Md5Join(key, specialKey)

	fmt.Println(x)
}
