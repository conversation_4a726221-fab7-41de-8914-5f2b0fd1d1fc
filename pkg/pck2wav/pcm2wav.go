package pck2wav

import (
	"fmt"
	"github.com/pkg/errors"
)

func NewWavHead() []byte {
	wavHead := newWav(uint16(1), 8000, uint16(16), 12400)
	head, err := wavHead.Marshal()
	if err != nil {
		panic(fmt.Sprintf("marshal wav header error: %v", err))
	}

	return head
}

func Pcm2Wav(pcm []byte, sampleRate int) ([]byte, error) {
	wavHead := newWav(uint16(1), uint32(sampleRate), uint16(16), uint32(len(pcm)))

	head, err := wavHead.Marshal()
	if err != nil {
		return nil, errors.Wrap(err, "marshal wav header error")
	}

	res := make([]byte, 0, len(head)+len(pcm))
	res = append(res, head...)
	res = append(res, pcm...)

	return res, nil
}
