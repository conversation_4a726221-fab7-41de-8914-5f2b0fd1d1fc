package alarm

import (
	"sync"
	"time"
)

// RateLimiter struct to manage the rate limiting
type RateLimiter struct {
	mu        sync.Mutex
	tokens    int
	maxTokens int
	interval  time.Duration
	ticker    *time.Ticker
}

// NewRateLimiter creates a new RateLimiter
func NewRateLimiter(maxTokens int, interval time.Duration) *RateLimiter {
	rl := &RateLimiter{
		tokens:    maxTokens,
		maxTokens: maxTokens,
		interval:  interval,
		ticker:    time.NewTicker(interval),
	}

	go rl.refillTokens()
	return rl
}

// refillTokens replenishes the tokens at the specified interval
func (rl *RateLimiter) refillTokens() {
	for range rl.ticker.C {
		rl.mu.Lock()
		rl.tokens = rl.maxTokens
		rl.mu.Unlock()
	}
}

// Allow checks if a token can be consumed
func (rl *RateLimiter) Allow() bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	if rl.tokens > 0 {
		rl.tokens--
		return true
	}
	return false
}

// Stop stops the ticker
func (rl *RateLimiter) Stop() {
	rl.ticker.Stop()
}
