package alarm

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"
	"net/http"
	"sync"
	"time"
	"tts-service/internal/pkg/environment"
	"tts-service/internal/pkg/log"
)

const (
	alarmTimeout      = 5 * time.Second
	alarmRetryTimeout = 1 * time.Second
	alarmTimes        = 3
)

var (
	alarmClient *Client
	alarmOnce   sync.Once

	rateLimiter     *RateLimiter
	rateLimiterOnce sync.Once
)

type Client struct {
	http *resty.Client
}

func InitRateLimiter() {
	rateLimiterOnce.Do(func() {
		rateLimiter = NewRateLimiter(100, time.Minute)
	})
}

func GetAlarmClient() *Client {
	alarmOnce.Do(func() {
		alarmClient = &Client{
			http: resty.New().
				SetTimeout(alarmTimeout).
				SetRetryCount(alarmTimes).
				SetRetryWaitTime(alarmRetryTimeout),
		}
	})

	return alarmClient
}

func (client *Client) Alarm(alarmUrl, user, title, body, groupName, company string, level int) error {
	InitRateLimiter()
	if !rateLimiter.Allow() {
		return nil
	}

	requestBody := map[string]interface{}{
		"username":   user,
		"supervisor": user,
		"title":      title,
		"body":       body,
		"dept":       groupName,
		"level":      level,
		"webhook":    "NotifyAll",
	}

	if company != "" {
		requestBody["receiver_type"] = "company"
		requestBody["username"] = company
	}

	if environment.Env.IsTest() {
		requestBody["webhook"] = "NotifyOnlyDeptWithoutAt"
	}

	jsonBody, _ := json.Marshal(requestBody)
	log.Infof(context.Background(), "alarm request body: %s", string(jsonBody))
	resp, err := client.http.R().
		SetHeader("Content-Type", "application/json").
		SetBody(jsonBody).
		Post(alarmUrl)

	if err != nil {
		return errors.Wrap(err, "alarm request error")
	}

	if resp.StatusCode() != http.StatusOK {
		return errors.New(fmt.Sprintf("alarm error, code=%d, body=%s", resp.StatusCode(), resp.Body()))
	}

	return nil
}
