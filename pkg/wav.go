package pkg

import (
	"context"
	"fmt"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"os"
	"os/exec"
	"tts-service/internal/pkg/log"
)

// ConvertMp3ToWav Convert mp3 file to wav file
func ConvertMp3ToWav(ctx context.Context, mp3Bytes []byte) ([]byte, error) {
	randomStr := uuid.New()
	realMp3Name := fmt.Sprintf("/tmp/%s.mp3", randomStr)
	realWavName := fmt.Sprintf("/tmp/%s.wav", randomStr)

	err := os.WriteFile(realMp3Name, mp3Bytes, 0644)
	if err != nil {
		return nil, errors.Wrap(err, "write mp3 file failed")
	}

	defer os.Remove(realMp3Name)

	cmd := exec.Command("ffmpeg", "-loglevel", "error", "-y", "-i", realMp3Name, "-acodec", "pcm_s16le", "-ac", "1", "-ar", "8000", realWavName)
	out, err := cmd.CombinedOutput()
	log.Infof(ctx, "HandleAudioFile result: %s", string(out))

	if err != nil {
		return nil, errors.New("ffmpeg conversion failed, err:" + err.Error())
	}

	defer os.Remove(realWavName)

	return os.ReadFile(realWavName)
}
