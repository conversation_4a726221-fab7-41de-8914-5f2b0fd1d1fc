// Copyright 2019 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

//go:build dragonfly || freebsd || linux || openbsd || solaris
// +build dragonfly freebsd linux openbsd solaris

package access_time

import (
	"syscall"
	"time"
)

func AccessTime(s *syscall.Stat_t) time.Time {
	return time.Unix(s.Atim.Unix())
}
