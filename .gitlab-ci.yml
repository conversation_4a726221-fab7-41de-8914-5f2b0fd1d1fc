sonarqube-check:
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [ "" ]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner -X -Dsonar.projectKey=paas-tts-service   -Dsonar.sources=.   -Dsonar.host.url=https://sonarqube.airudder.com   -Dsonar.login=**************************************** -Dsonar.branch.name=$CI_COMMIT_BRANCH
  allow_failure: true
