// server: TTS
// client: DM

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.29.3
// source: realtime_tts.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Config_CommandType int32

const (
	Config_Audio  Config_CommandType = 0
	Config_Cancel Config_CommandType = 1
)

// Enum value maps for Config_CommandType.
var (
	Config_CommandType_name = map[int32]string{
		0: "Audio",
		1: "Cancel",
	}
	Config_CommandType_value = map[string]int32{
		"Audio":  0,
		"Cancel": 1,
	}
)

func (x Config_CommandType) Enum() *Config_CommandType {
	p := new(Config_CommandType)
	*p = x
	return p
}

func (x Config_CommandType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Config_CommandType) Descriptor() protoreflect.EnumDescriptor {
	return file_realtime_tts_proto_enumTypes[0].Descriptor()
}

func (Config_CommandType) Type() protoreflect.EnumType {
	return &file_realtime_tts_proto_enumTypes[0]
}

func (x Config_CommandType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Config_CommandType.Descriptor instead.
func (Config_CommandType) EnumDescriptor() ([]byte, []int) {
	return file_realtime_tts_proto_rawDescGZIP(), []int{2, 0}
}

type DoubleStreamContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text  string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	IsEnd bool   `protobuf:"varint,2,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
}

func (x *DoubleStreamContent) Reset() {
	*x = DoubleStreamContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_tts_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleStreamContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleStreamContent) ProtoMessage() {}

func (x *DoubleStreamContent) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_tts_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleStreamContent.ProtoReflect.Descriptor instead.
func (*DoubleStreamContent) Descriptor() ([]byte, []int) {
	return file_realtime_tts_proto_rawDescGZIP(), []int{0}
}

func (x *DoubleStreamContent) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *DoubleStreamContent) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

type DoubleStreamConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//	*DoubleStreamConfig_Config
	//	*DoubleStreamConfig_Content
	Request    isDoubleStreamConfig_Request `protobuf_oneof:"request"`
	RequestKey string                       `protobuf:"bytes,3,opt,name=request_key,json=requestKey,proto3" json:"request_key,omitempty"`
	NeedCache  bool                         `protobuf:"varint,4,opt,name=need_cache,json=needCache,proto3" json:"need_cache,omitempty"`
}

func (x *DoubleStreamConfig) Reset() {
	*x = DoubleStreamConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_tts_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleStreamConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleStreamConfig) ProtoMessage() {}

func (x *DoubleStreamConfig) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_tts_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleStreamConfig.ProtoReflect.Descriptor instead.
func (*DoubleStreamConfig) Descriptor() ([]byte, []int) {
	return file_realtime_tts_proto_rawDescGZIP(), []int{1}
}

func (m *DoubleStreamConfig) GetRequest() isDoubleStreamConfig_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *DoubleStreamConfig) GetConfig() *Config {
	if x, ok := x.GetRequest().(*DoubleStreamConfig_Config); ok {
		return x.Config
	}
	return nil
}

func (x *DoubleStreamConfig) GetContent() *DoubleStreamContent {
	if x, ok := x.GetRequest().(*DoubleStreamConfig_Content); ok {
		return x.Content
	}
	return nil
}

func (x *DoubleStreamConfig) GetRequestKey() string {
	if x != nil {
		return x.RequestKey
	}
	return ""
}

func (x *DoubleStreamConfig) GetNeedCache() bool {
	if x != nil {
		return x.NeedCache
	}
	return false
}

type isDoubleStreamConfig_Request interface {
	isDoubleStreamConfig_Request()
}

type DoubleStreamConfig_Config struct {
	Config *Config `protobuf:"bytes,1,opt,name=config,proto3,oneof"`
}

type DoubleStreamConfig_Content struct {
	Content *DoubleStreamContent `protobuf:"bytes,2,opt,name=content,proto3,oneof"`
}

func (*DoubleStreamConfig_Config) isDoubleStreamConfig_Request() {}

func (*DoubleStreamConfig_Content) isDoubleStreamConfig_Request() {}

type Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp    int64   `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Speaker      string  `protobuf:"bytes,2,opt,name=speaker,proto3" json:"speaker,omitempty"`
	TtsKey       string  `protobuf:"bytes,3,opt,name=tts_key,json=ttsKey,proto3" json:"tts_key,omitempty"`
	TtsType      string  `protobuf:"bytes,4,opt,name=tts_type,json=ttsType,proto3" json:"tts_type,omitempty"`
	SpeakingRate float32 `protobuf:"fixed32,5,opt,name=speaking_rate,json=speakingRate,proto3" json:"speaking_rate,omitempty"`
	Volume       float32 `protobuf:"fixed32,6,opt,name=volume,proto3" json:"volume,omitempty"`
	Mode         string  `protobuf:"bytes,7,opt,name=mode,proto3" json:"mode,omitempty"`
	ServerName   string  `protobuf:"bytes,8,opt,name=server_name,json=serverName,proto3" json:"server_name,omitempty"`
	SampleRate   int32   `protobuf:"varint,9,opt,name=sample_rate,json=sampleRate,proto3" json:"sample_rate,omitempty"`
	FromWhere    string  `protobuf:"bytes,10,opt,name=from_where,json=fromWhere,proto3" json:"from_where,omitempty"`
	AccountType  string  `protobuf:"bytes,11,opt,name=account_type,json=accountType,proto3" json:"account_type,omitempty"`
	Locale       string  `protobuf:"bytes,12,opt,name=locale,proto3" json:"locale,omitempty"`
	CallId       string  `protobuf:"bytes,13,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	TtsV3        bool    `protobuf:"varint,14,opt,name=tts_v3,json=ttsV3,proto3" json:"tts_v3,omitempty"`
	// 待合成文本，支持ssml格式，如果是ssml格式，必须以<speak>开始，以</speak>结尾
	Text []string `protobuf:"bytes,15,rep,name=text,proto3" json:"text,omitempty"` // 待合成文本
	// 音频内容的参考文本，可选，如果有，和reference_audio_contents一一对应
	// 支持ssml格式，如果是ssml格式，每个文本必须以<speak>开始，以</speak>结尾
	RefText []string `protobuf:"bytes,16,rep,name=ref_text,json=refText,proto3" json:"ref_text,omitempty"`
	// 拼接的参考音频，必须
	// wav格式的数据(不包含wav文件头), 8k, 16bit, 小端
	ReferenceAudioContents [][]byte `protobuf:"bytes,17,rep,name=reference_audio_contents,json=referenceAudioContents,proto3" json:"reference_audio_contents,omitempty"`
	// 拼接的参考音频在整句中的位置, eg: [1, 0, 1]代表拼接的顺序为[音频, TTS, 音频]
	TextAndAudioPositions []bool `protobuf:"varint,18,rep,packed,name=text_and_audio_positions,json=textAndAudioPositions,proto3" json:"text_and_audio_positions,omitempty"`
	RobotName             string `protobuf:"bytes,19,opt,name=robot_name,json=robotName,proto3" json:"robot_name,omitempty"`
	AppName               string `protobuf:"bytes,20,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	Stream                bool   `protobuf:"varint,21,opt,name=stream,proto3" json:"stream,omitempty"`
	RequestKey            string `protobuf:"bytes,22,opt,name=request_key,json=requestKey,proto3" json:"request_key,omitempty"`
	Company               string `protobuf:"bytes,23,opt,name=company,proto3" json:"company,omitempty"`
	UniqueKey             string `protobuf:"bytes,24,opt,name=unique_key,json=uniqueKey,proto3" json:"unique_key,omitempty"`
	// 用来表示请求类型，默认表示为音频请求
	CommandType Config_CommandType `protobuf:"varint,25,opt,name=command_type,json=commandType,proto3,enum=Config_CommandType" json:"command_type,omitempty"`
	// 字符串数组，用来标识撤销列表（当 command_type=Cancel 的时候生效，此时其他字段可忽略）
	CancelRequestKeys []string `protobuf:"bytes,26,rep,name=cancel_request_keys,json=cancelRequestKeys,proto3" json:"cancel_request_keys,omitempty"`
	Version           string   `protobuf:"bytes,27,opt,name=version,proto3" json:"version,omitempty"`
	VoiceId           string   `protobuf:"bytes,28,opt,name=voice_id,json=voiceId,proto3" json:"voice_id,omitempty"`
	Extra             string   `protobuf:"bytes,29,opt,name=extra,proto3" json:"extra,omitempty"`
	StateId           string   `protobuf:"bytes,30,opt,name=state_id,json=stateId,proto3" json:"state_id,omitempty"`
}

func (x *Config) Reset() {
	*x = Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_tts_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_tts_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_realtime_tts_proto_rawDescGZIP(), []int{2}
}

func (x *Config) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *Config) GetSpeaker() string {
	if x != nil {
		return x.Speaker
	}
	return ""
}

func (x *Config) GetTtsKey() string {
	if x != nil {
		return x.TtsKey
	}
	return ""
}

func (x *Config) GetTtsType() string {
	if x != nil {
		return x.TtsType
	}
	return ""
}

func (x *Config) GetSpeakingRate() float32 {
	if x != nil {
		return x.SpeakingRate
	}
	return 0
}

func (x *Config) GetVolume() float32 {
	if x != nil {
		return x.Volume
	}
	return 0
}

func (x *Config) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

func (x *Config) GetServerName() string {
	if x != nil {
		return x.ServerName
	}
	return ""
}

func (x *Config) GetSampleRate() int32 {
	if x != nil {
		return x.SampleRate
	}
	return 0
}

func (x *Config) GetFromWhere() string {
	if x != nil {
		return x.FromWhere
	}
	return ""
}

func (x *Config) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

func (x *Config) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

func (x *Config) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (x *Config) GetTtsV3() bool {
	if x != nil {
		return x.TtsV3
	}
	return false
}

func (x *Config) GetText() []string {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *Config) GetRefText() []string {
	if x != nil {
		return x.RefText
	}
	return nil
}

func (x *Config) GetReferenceAudioContents() [][]byte {
	if x != nil {
		return x.ReferenceAudioContents
	}
	return nil
}

func (x *Config) GetTextAndAudioPositions() []bool {
	if x != nil {
		return x.TextAndAudioPositions
	}
	return nil
}

func (x *Config) GetRobotName() string {
	if x != nil {
		return x.RobotName
	}
	return ""
}

func (x *Config) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *Config) GetStream() bool {
	if x != nil {
		return x.Stream
	}
	return false
}

func (x *Config) GetRequestKey() string {
	if x != nil {
		return x.RequestKey
	}
	return ""
}

func (x *Config) GetCompany() string {
	if x != nil {
		return x.Company
	}
	return ""
}

func (x *Config) GetUniqueKey() string {
	if x != nil {
		return x.UniqueKey
	}
	return ""
}

func (x *Config) GetCommandType() Config_CommandType {
	if x != nil {
		return x.CommandType
	}
	return Config_Audio
}

func (x *Config) GetCancelRequestKeys() []string {
	if x != nil {
		return x.CancelRequestKeys
	}
	return nil
}

func (x *Config) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Config) GetVoiceId() string {
	if x != nil {
		return x.VoiceId
	}
	return ""
}

func (x *Config) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *Config) GetStateId() string {
	if x != nil {
		return x.StateId
	}
	return ""
}

type Audio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp    int64  `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Code         int64  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Msg          string `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
	AudioContent []byte `protobuf:"bytes,4,opt,name=audio_content,json=audioContent,proto3" json:"audio_content,omitempty"`
	// 命中缓存
	UseCache bool    `protobuf:"varint,5,opt,name=use_cache,json=useCache,proto3" json:"use_cache,omitempty"`
	Duration float32 `protobuf:"fixed32,6,opt,name=duration,proto3" json:"duration,omitempty"`
	// 接收时间
	AcceptTime int64 `protobuf:"varint,7,opt,name=accept_time,json=acceptTime,proto3" json:"accept_time,omitempty"`
	// 调用 tts 的生成时间
	GenerateTime int64 `protobuf:"varint,8,opt,name=generate_time,json=generateTime,proto3" json:"generate_time,omitempty"`
	// tts 生成成功时间
	GeneratedTime int64 `protobuf:"varint,9,opt,name=generated_time,json=generatedTime,proto3" json:"generated_time,omitempty"`
	// tts 缓存并计算长度时间
	EndTime int64 `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 自定义的特殊字段，供后续扩展
	CustomConfig string `protobuf:"bytes,11,opt,name=custom_config,json=customConfig,proto3" json:"custom_config,omitempty"`
	NeedRepeated bool   `protobuf:"varint,12,opt,name=need_repeated,json=needRepeated,proto3" json:"need_repeated,omitempty"`
	// 流式TTS结束
	IsEnd        bool         `protobuf:"varint,13,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	TtsKey       string       `protobuf:"bytes,14,opt,name=tts_key,json=ttsKey,proto3" json:"tts_key,omitempty"`
	DelayTime    float64      `protobuf:"fixed64,15,opt,name=delay_time,json=delayTime,proto3" json:"delay_time,omitempty"`
	RequestKey   string       `protobuf:"bytes,16,opt,name=request_key,json=requestKey,proto3" json:"request_key,omitempty"`
	Words        []*AudioWord `protobuf:"bytes,17,rep,name=words,proto3" json:"words,omitempty"`
	CacheVersion string       `protobuf:"bytes,18,opt,name=cache_version,json=cacheVersion,proto3" json:"cache_version,omitempty"`
	CacheKey     string       `protobuf:"bytes,19,opt,name=cache_key,json=cacheKey,proto3" json:"cache_key,omitempty"`
}

func (x *Audio) Reset() {
	*x = Audio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_tts_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Audio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Audio) ProtoMessage() {}

func (x *Audio) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_tts_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Audio.ProtoReflect.Descriptor instead.
func (*Audio) Descriptor() ([]byte, []int) {
	return file_realtime_tts_proto_rawDescGZIP(), []int{3}
}

func (x *Audio) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *Audio) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Audio) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *Audio) GetAudioContent() []byte {
	if x != nil {
		return x.AudioContent
	}
	return nil
}

func (x *Audio) GetUseCache() bool {
	if x != nil {
		return x.UseCache
	}
	return false
}

func (x *Audio) GetDuration() float32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *Audio) GetAcceptTime() int64 {
	if x != nil {
		return x.AcceptTime
	}
	return 0
}

func (x *Audio) GetGenerateTime() int64 {
	if x != nil {
		return x.GenerateTime
	}
	return 0
}

func (x *Audio) GetGeneratedTime() int64 {
	if x != nil {
		return x.GeneratedTime
	}
	return 0
}

func (x *Audio) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Audio) GetCustomConfig() string {
	if x != nil {
		return x.CustomConfig
	}
	return ""
}

func (x *Audio) GetNeedRepeated() bool {
	if x != nil {
		return x.NeedRepeated
	}
	return false
}

func (x *Audio) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

func (x *Audio) GetTtsKey() string {
	if x != nil {
		return x.TtsKey
	}
	return ""
}

func (x *Audio) GetDelayTime() float64 {
	if x != nil {
		return x.DelayTime
	}
	return 0
}

func (x *Audio) GetRequestKey() string {
	if x != nil {
		return x.RequestKey
	}
	return ""
}

func (x *Audio) GetWords() []*AudioWord {
	if x != nil {
		return x.Words
	}
	return nil
}

func (x *Audio) GetCacheVersion() string {
	if x != nil {
		return x.CacheVersion
	}
	return ""
}

func (x *Audio) GetCacheKey() string {
	if x != nil {
		return x.CacheKey
	}
	return ""
}

type AudioWord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Word     string `protobuf:"bytes,1,opt,name=word,proto3" json:"word,omitempty"`
	Duration int64  `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (x *AudioWord) Reset() {
	*x = AudioWord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_tts_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudioWord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioWord) ProtoMessage() {}

func (x *AudioWord) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_tts_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioWord.ProtoReflect.Descriptor instead.
func (*AudioWord) Descriptor() ([]byte, []int) {
	return file_realtime_tts_proto_rawDescGZIP(), []int{3, 0}
}

func (x *AudioWord) GetWord() string {
	if x != nil {
		return x.Word
	}
	return ""
}

func (x *AudioWord) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

var File_realtime_tts_proto protoreflect.FileDescriptor

var file_realtime_tts_proto_rawDesc = []byte{
	0x0a, 0x12, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x74, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x40, 0x0a, 0x13, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x53, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12,
	0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x05, 0x69, 0x73, 0x45, 0x6e, 0x64, 0x22, 0xb4, 0x01, 0x0a, 0x12, 0x44, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x21, 0x0a,
	0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x07, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x30, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x63, 0x68,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6e, 0x65, 0x65, 0x64, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xd3, 0x07,
	0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x74, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x74, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x74, 0x73,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x74, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x73, 0x70, 0x65,
	0x61, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x61, 0x6d,
	0x70, 0x6c, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x5f,
	0x77, 0x68, 0x65, 0x72, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x72, 0x6f,
	0x6d, 0x57, 0x68, 0x65, 0x72, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x74,
	0x73, 0x5f, 0x76, 0x33, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x74, 0x74, 0x73, 0x56,
	0x33, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x66, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x66, 0x54, 0x65, 0x78, 0x74,
	0x12, 0x38, 0x0a, 0x18, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x75,
	0x64, 0x69, 0x6f, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x11, 0x20, 0x03,
	0x28, 0x0c, 0x52, 0x16, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x75, 0x64,
	0x69, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x74, 0x65,
	0x78, 0x74, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x08, 0x52, 0x15, 0x74, 0x65,
	0x78, 0x74, 0x41, 0x6e, 0x64, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12,
	0x36, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x1a,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x1c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x1e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x61, 0x74, 0x65, 0x49, 0x64, 0x22, 0x24, 0x0a,
	0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05,
	0x41, 0x75, 0x64, 0x69, 0x6f, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x10, 0x01, 0x22, 0x88, 0x05, 0x0a, 0x05, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x5f, 0x63, 0x61,
	0x63, 0x68, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x75, 0x73, 0x65, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x23, 0x0a, 0x0d,
	0x6e, 0x65, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0c, 0x6e, 0x65, 0x65, 0x64, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x05, 0x69, 0x73, 0x45, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x74, 0x73, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x74, 0x73, 0x4b, 0x65,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4b, 0x65,
	0x79, 0x12, 0x21, 0x0a, 0x05, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x2e, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x05, 0x77,
	0x6f, 0x72, 0x64, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x63,
	0x68, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x63,
	0x68, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61,
	0x63, 0x68, 0x65, 0x4b, 0x65, 0x79, 0x1a, 0x36, 0x0a, 0x04, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x77, 0x6f,
	0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x98,
	0x01, 0x0a, 0x0b, 0x52, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x54, 0x54, 0x53, 0x12, 0x1d,
	0x0a, 0x08, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x12, 0x07, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x1a, 0x06, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x22, 0x00, 0x12, 0x2b, 0x0a,
	0x12, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x57, 0x69, 0x74, 0x68, 0x53, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x12, 0x07, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x06, 0x2e, 0x61,
	0x75, 0x64, 0x69, 0x6f, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x3d, 0x0a, 0x18, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x57, 0x69, 0x74, 0x68, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x13, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x53,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x06, 0x2e, 0x61, 0x75,
	0x64, 0x69, 0x6f, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_realtime_tts_proto_rawDescOnce sync.Once
	file_realtime_tts_proto_rawDescData = file_realtime_tts_proto_rawDesc
)

func file_realtime_tts_proto_rawDescGZIP() []byte {
	file_realtime_tts_proto_rawDescOnce.Do(func() {
		file_realtime_tts_proto_rawDescData = protoimpl.X.CompressGZIP(file_realtime_tts_proto_rawDescData)
	})
	return file_realtime_tts_proto_rawDescData
}

var file_realtime_tts_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_realtime_tts_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_realtime_tts_proto_goTypes = []interface{}{
	(Config_CommandType)(0),     // 0: config.CommandType
	(*DoubleStreamContent)(nil), // 1: DoubleStreamContent
	(*DoubleStreamConfig)(nil),  // 2: DoubleStreamConfig
	(*Config)(nil),              // 3: config
	(*Audio)(nil),               // 4: audio
	(*AudioWord)(nil),           // 5: audio.word
}
var file_realtime_tts_proto_depIdxs = []int32{
	3, // 0: DoubleStreamConfig.config:type_name -> config
	1, // 1: DoubleStreamConfig.content:type_name -> DoubleStreamContent
	0, // 2: config.command_type:type_name -> config.CommandType
	5, // 3: audio.words:type_name -> audio.word
	3, // 4: RealtimeTTS.Generate:input_type -> config
	3, // 5: RealtimeTTS.GenerateWithStream:input_type -> config
	2, // 6: RealtimeTTS.GenerateWithDoubleStream:input_type -> DoubleStreamConfig
	4, // 7: RealtimeTTS.Generate:output_type -> audio
	4, // 8: RealtimeTTS.GenerateWithStream:output_type -> audio
	4, // 9: RealtimeTTS.GenerateWithDoubleStream:output_type -> audio
	7, // [7:10] is the sub-list for method output_type
	4, // [4:7] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_realtime_tts_proto_init() }
func file_realtime_tts_proto_init() {
	if File_realtime_tts_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_realtime_tts_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoubleStreamContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_tts_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoubleStreamConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_tts_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_tts_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Audio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_tts_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudioWord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_realtime_tts_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*DoubleStreamConfig_Config)(nil),
		(*DoubleStreamConfig_Content)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_realtime_tts_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_realtime_tts_proto_goTypes,
		DependencyIndexes: file_realtime_tts_proto_depIdxs,
		EnumInfos:         file_realtime_tts_proto_enumTypes,
		MessageInfos:      file_realtime_tts_proto_msgTypes,
	}.Build()
	File_realtime_tts_proto = out.File
	file_realtime_tts_proto_rawDesc = nil
	file_realtime_tts_proto_goTypes = nil
	file_realtime_tts_proto_depIdxs = nil
}
