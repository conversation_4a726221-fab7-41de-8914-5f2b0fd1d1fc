// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v5.29.3
// source: realtime_tts.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// RealtimeTTSClient is the client API for RealtimeTTS service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RealtimeTTSClient interface {
	Generate(ctx context.Context, in *Config, opts ...grpc.CallOption) (*Audio, error)
	GenerateWithStream(ctx context.Context, opts ...grpc.CallOption) (RealtimeTTS_GenerateWithStreamClient, error)
	GenerateWithDoubleStream(ctx context.Context, opts ...grpc.CallOption) (RealtimeTTS_GenerateWithDoubleStreamClient, error)
}

type realtimeTTSClient struct {
	cc grpc.ClientConnInterface
}

func NewRealtimeTTSClient(cc grpc.ClientConnInterface) RealtimeTTSClient {
	return &realtimeTTSClient{cc}
}

func (c *realtimeTTSClient) Generate(ctx context.Context, in *Config, opts ...grpc.CallOption) (*Audio, error) {
	out := new(Audio)
	err := c.cc.Invoke(ctx, "/RealtimeTTS/Generate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realtimeTTSClient) GenerateWithStream(ctx context.Context, opts ...grpc.CallOption) (RealtimeTTS_GenerateWithStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &RealtimeTTS_ServiceDesc.Streams[0], "/RealtimeTTS/GenerateWithStream", opts...)
	if err != nil {
		return nil, err
	}
	x := &realtimeTTSGenerateWithStreamClient{stream}
	return x, nil
}

type RealtimeTTS_GenerateWithStreamClient interface {
	Send(*Config) error
	Recv() (*Audio, error)
	grpc.ClientStream
}

type realtimeTTSGenerateWithStreamClient struct {
	grpc.ClientStream
}

func (x *realtimeTTSGenerateWithStreamClient) Send(m *Config) error {
	return x.ClientStream.SendMsg(m)
}

func (x *realtimeTTSGenerateWithStreamClient) Recv() (*Audio, error) {
	m := new(Audio)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *realtimeTTSClient) GenerateWithDoubleStream(ctx context.Context, opts ...grpc.CallOption) (RealtimeTTS_GenerateWithDoubleStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &RealtimeTTS_ServiceDesc.Streams[1], "/RealtimeTTS/GenerateWithDoubleStream", opts...)
	if err != nil {
		return nil, err
	}
	x := &realtimeTTSGenerateWithDoubleStreamClient{stream}
	return x, nil
}

type RealtimeTTS_GenerateWithDoubleStreamClient interface {
	Send(*DoubleStreamConfig) error
	Recv() (*Audio, error)
	grpc.ClientStream
}

type realtimeTTSGenerateWithDoubleStreamClient struct {
	grpc.ClientStream
}

func (x *realtimeTTSGenerateWithDoubleStreamClient) Send(m *DoubleStreamConfig) error {
	return x.ClientStream.SendMsg(m)
}

func (x *realtimeTTSGenerateWithDoubleStreamClient) Recv() (*Audio, error) {
	m := new(Audio)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RealtimeTTSServer is the server API for RealtimeTTS service.
// All implementations must embed UnimplementedRealtimeTTSServer
// for forward compatibility
type RealtimeTTSServer interface {
	Generate(context.Context, *Config) (*Audio, error)
	GenerateWithStream(RealtimeTTS_GenerateWithStreamServer) error
	GenerateWithDoubleStream(RealtimeTTS_GenerateWithDoubleStreamServer) error
	mustEmbedUnimplementedRealtimeTTSServer()
}

// UnimplementedRealtimeTTSServer must be embedded to have forward compatible implementations.
type UnimplementedRealtimeTTSServer struct {
}

func (UnimplementedRealtimeTTSServer) Generate(context.Context, *Config) (*Audio, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Generate not implemented")
}
func (UnimplementedRealtimeTTSServer) GenerateWithStream(RealtimeTTS_GenerateWithStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method GenerateWithStream not implemented")
}
func (UnimplementedRealtimeTTSServer) GenerateWithDoubleStream(RealtimeTTS_GenerateWithDoubleStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method GenerateWithDoubleStream not implemented")
}
func (UnimplementedRealtimeTTSServer) mustEmbedUnimplementedRealtimeTTSServer() {}

// UnsafeRealtimeTTSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RealtimeTTSServer will
// result in compilation errors.
type UnsafeRealtimeTTSServer interface {
	mustEmbedUnimplementedRealtimeTTSServer()
}

func RegisterRealtimeTTSServer(s grpc.ServiceRegistrar, srv RealtimeTTSServer) {
	s.RegisterService(&RealtimeTTS_ServiceDesc, srv)
}

func _RealtimeTTS_Generate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Config)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealtimeTTSServer).Generate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/RealtimeTTS/Generate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealtimeTTSServer).Generate(ctx, req.(*Config))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealtimeTTS_GenerateWithStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(RealtimeTTSServer).GenerateWithStream(&realtimeTTSGenerateWithStreamServer{stream})
}

type RealtimeTTS_GenerateWithStreamServer interface {
	Send(*Audio) error
	Recv() (*Config, error)
	grpc.ServerStream
}

type realtimeTTSGenerateWithStreamServer struct {
	grpc.ServerStream
}

func (x *realtimeTTSGenerateWithStreamServer) Send(m *Audio) error {
	return x.ServerStream.SendMsg(m)
}

func (x *realtimeTTSGenerateWithStreamServer) Recv() (*Config, error) {
	m := new(Config)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _RealtimeTTS_GenerateWithDoubleStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(RealtimeTTSServer).GenerateWithDoubleStream(&realtimeTTSGenerateWithDoubleStreamServer{stream})
}

type RealtimeTTS_GenerateWithDoubleStreamServer interface {
	Send(*Audio) error
	Recv() (*DoubleStreamConfig, error)
	grpc.ServerStream
}

type realtimeTTSGenerateWithDoubleStreamServer struct {
	grpc.ServerStream
}

func (x *realtimeTTSGenerateWithDoubleStreamServer) Send(m *Audio) error {
	return x.ServerStream.SendMsg(m)
}

func (x *realtimeTTSGenerateWithDoubleStreamServer) Recv() (*DoubleStreamConfig, error) {
	m := new(DoubleStreamConfig)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RealtimeTTS_ServiceDesc is the grpc.ServiceDesc for RealtimeTTS service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RealtimeTTS_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "RealtimeTTS",
	HandlerType: (*RealtimeTTSServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Generate",
			Handler:    _RealtimeTTS_Generate_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GenerateWithStream",
			Handler:       _RealtimeTTS_GenerateWithStream_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "GenerateWithDoubleStream",
			Handler:       _RealtimeTTS_GenerateWithDoubleStream_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "realtime_tts.proto",
}
