// server: TTS
// client: DM

syntax = "proto3";

option go_package = "./pb";

service RealtimeTTS {
  rpc Generate (config) returns (audio) {}
  rpc GenerateWithStream (stream config) returns (stream audio) {}
  rpc GenerateWithDoubleStream (stream DoubleStreamConfig) returns (stream audio) {}
}

message DoubleStreamContent {
  string text = 1;
  bool is_end = 2;
}

message DoubleStreamConfig {
  oneof request {
    config config = 1;
    DoubleStreamContent content = 2;
  }

  string request_key = 3;
  bool need_cache = 4;
}

message config {
  enum CommandType {
    Audio = 0;
    Cancel = 1;
  }

  int64 timestamp = 1;
  string speaker = 2;
  string tts_key = 3;
  string tts_type = 4;
  float speaking_rate = 5;
  float volume = 6;
  string mode = 7;
  string server_name = 8;
  int32 sample_rate = 9;
  string from_where = 10;
  string account_type = 11;
  string locale = 12;
  string call_id = 13;
  bool tts_v3 = 14;

  // 待合成文本，支持ssml格式，如果是ssml格式，必须以<speak>开始，以</speak>结尾
  repeated string text = 15;  // 待合成文本
  // 音频内容的参考文本，可选，如果有，和reference_audio_contents一一对应
  // 支持ssml格式，如果是ssml格式，每个文本必须以<speak>开始，以</speak>结尾
  repeated string ref_text = 16;
  // 拼接的参考音频，必须
  // wav格式的数据(不包含wav文件头), 8k, 16bit, 小端
  repeated bytes reference_audio_contents = 17;
  // 拼接的参考音频在整句中的位置, eg: [1, 0, 1]代表拼接的顺序为[音频, TTS, 音频]
  repeated bool text_and_audio_positions = 18;

  string robot_name = 19;
  string app_name = 20;
  bool stream = 21;
  string request_key = 22;
  string company = 23;
  string unique_key = 24;

  // 用来表示请求类型，默认表示为音频请求
  CommandType command_type = 25;
  // 字符串数组，用来标识撤销列表（当 command_type=Cancel 的时候生效，此时其他字段可忽略）
  repeated string cancel_request_keys = 26;
  string version = 27;
  string voice_id = 28;
  string extra = 29;
  string state_id = 30;
}


message audio {

  message word {
    string word = 1;
    int64 duration = 2;
  }

  int64 timestamp = 1;
  int64 code = 2;
  string msg = 3;
  bytes audio_content = 4;
  // 命中缓存
  bool use_cache = 5;
  float duration = 6;
  // 接收时间
  int64 accept_time = 7;
  // 调用 tts 的生成时间
  int64 generate_time = 8;
  // tts 生成成功时间
  int64 generated_time = 9;
  // tts 缓存并计算长度时间
  int64 end_time = 10;

  // 自定义的特殊字段，供后续扩展
  string custom_config = 11;

  bool need_repeated = 12;

  // 流式TTS结束
  bool is_end = 13;
  string tts_key = 14;
  double delay_time = 15;
  string request_key = 16;

  repeated word words = 17;
  string cache_version = 18;
  string cache_key = 19;
}