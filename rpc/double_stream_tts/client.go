package double_stream_tts

import (
	"context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"sync"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/log"
	"tts-service/rpc/double_stream_tts/double_stream"
)

var (
	ttsDoubleStreamClient    *grpc.ClientConn
	ttsDoubleStreamClientOne sync.Once
)

func getDoubleStreamTTSClient(ctx context.Context, address string) *grpc.ClientConn {
	ttsDoubleStreamClientOne.Do(func() {
		client, err := grpc.Dial(address, grpc.WithTransportCredentials(insecure.NewCredentials()))
		if err != nil {
			log.Errorf(ctx, "Failed to dial tts server: %s", err.Error())
			ttsDoubleStreamClientOne = sync.Once{}
		}

		ttsDoubleStreamClient = client
	})
	return ttsDoubleStreamClient
}

func GetDoubleStreamTTSClient(ctx context.Context) double_stream.TtsClient {
	return double_stream.NewTtsClient(getDoubleStreamTTSClient(ctx, config.Config.GetString("services.double_stream_addr")))
}
