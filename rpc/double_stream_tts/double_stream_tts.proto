syntax = "proto3";
import "google/protobuf/empty.proto";
package io.nopause.tts;
option go_package = "./double_stream";

message VoiceConfig {
  string language_code = 1;  // Language name: en-US
  string voice_id = 2;       // Voice name: en-US-Mike
}

message AudioConfig {
  string audio_encoding = 1;  // Audio format, such as wav
  float speaking_rate = 2;    // Speaking rate, 1.0 unchanged, must be passed
  // because the default is 0
  float volume_gain_db = 3;   // Volume gain, output original volume when 0, the
  // larger the value, the larger the volume
  int32 sample_rate_hertz = 4;  // Sampling rate
}

message TtsConfig {
  bool stream_in_off = 1;   // Whether to turn off input streaming
  bool stream_out_off = 2;  // Whether to turn off stream output streaming
  VoiceConfig voice = 3;    // Voice-related configuration
  AudioConfig audio = 4;    // Audio-related configuration
}

message TtsContent {
  string text = 1;
  bool is_end = 2;
}

message Authentication {
  string key = 1;  // secret key
}

message TtsRequest {
  oneof request {
    TtsConfig config = 1;    // TTS configuration, should only send once, before
    TtsContent content = 2;  // TTS content
  }
  Authentication authentication = 3;  // Authentication information
}

message TtsResponseMeta {
  string request_id = 1;  // Request ID for troubleshooting
  // Initial synthesis time of the current sentence, UNIX_TIME (ms)
  int64 synthesis_begin_ms = 2;
  int64 first_chunk_latency_ms = 3;  // First block latency
}

message TtsResponseChunkMeta {
  // real-time factor of current block, synthesis time divided
  // by audio length,
  float rtf = 1;
  // Current block synthesis start time, UNIX_TIME (ms)
  int64 chunk_synthesis_begin_ms = 2;
  int32 chunk_id = 3;  // Which streaming block is currently, starting from 0
  int64 chunk_size_us = 4;  // The size of the current block (us)
}

message TtsResponse {
  bytes audio_content = 1;  // Synthesized audio
  // Status, return OK after success or failure information otherwise
  string status = 2;
  bool is_end = 3;  // Whether it is the last block of streaming, also set to
  // True in non-streaming situations
  int32 code = 4;   // Return code, 0 for OK, other values ​​for error codes
  // Tts response meta info of every chunk
  TtsResponseChunkMeta tts_response_chunk_meta = 5;
  // Tts response meta info, only return along with the first chunk
  TtsResponseMeta tts_response_meta = 6;
}

message TtsSupportedLangSpeaker {
  repeated string languages = 1;
  repeated string speakers = 2;
}

service Tts {
  rpc Synthesis(stream TtsRequest) returns (stream TtsResponse) {}
  // get supported languages and speakers
  rpc SupportedLangSpeaker(google.protobuf.Empty)
      returns (TtsSupportedLangSpeaker) {}
}
