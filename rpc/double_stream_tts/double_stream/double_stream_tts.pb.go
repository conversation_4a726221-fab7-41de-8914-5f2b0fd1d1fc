// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.19.4
// source: double_stream_tts.proto

package double_stream

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VoiceConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageCode string `protobuf:"bytes,1,opt,name=language_code,json=languageCode,proto3" json:"language_code,omitempty"` // Language name: en-US
	VoiceId      string `protobuf:"bytes,2,opt,name=voice_id,json=voiceId,proto3" json:"voice_id,omitempty"`                // Voice name: en-US-Mike
}

func (x *VoiceConfig) Reset() {
	*x = VoiceConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_double_stream_tts_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoiceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoiceConfig) ProtoMessage() {}

func (x *VoiceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_double_stream_tts_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoiceConfig.ProtoReflect.Descriptor instead.
func (*VoiceConfig) Descriptor() ([]byte, []int) {
	return file_double_stream_tts_proto_rawDescGZIP(), []int{0}
}

func (x *VoiceConfig) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *VoiceConfig) GetVoiceId() string {
	if x != nil {
		return x.VoiceId
	}
	return ""
}

type AudioConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AudioEncoding string  `protobuf:"bytes,1,opt,name=audio_encoding,json=audioEncoding,proto3" json:"audio_encoding,omitempty"` // Audio format, such as wav
	SpeakingRate  float32 `protobuf:"fixed32,2,opt,name=speaking_rate,json=speakingRate,proto3" json:"speaking_rate,omitempty"`  // Speaking rate, 1.0 unchanged, must be passed
	// because the default is 0
	VolumeGainDb float32 `protobuf:"fixed32,3,opt,name=volume_gain_db,json=volumeGainDb,proto3" json:"volume_gain_db,omitempty"` // Volume gain, output original volume when 0, the
	// larger the value, the larger the volume
	SampleRateHertz int32 `protobuf:"varint,4,opt,name=sample_rate_hertz,json=sampleRateHertz,proto3" json:"sample_rate_hertz,omitempty"` // Sampling rate
}

func (x *AudioConfig) Reset() {
	*x = AudioConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_double_stream_tts_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudioConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioConfig) ProtoMessage() {}

func (x *AudioConfig) ProtoReflect() protoreflect.Message {
	mi := &file_double_stream_tts_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioConfig.ProtoReflect.Descriptor instead.
func (*AudioConfig) Descriptor() ([]byte, []int) {
	return file_double_stream_tts_proto_rawDescGZIP(), []int{1}
}

func (x *AudioConfig) GetAudioEncoding() string {
	if x != nil {
		return x.AudioEncoding
	}
	return ""
}

func (x *AudioConfig) GetSpeakingRate() float32 {
	if x != nil {
		return x.SpeakingRate
	}
	return 0
}

func (x *AudioConfig) GetVolumeGainDb() float32 {
	if x != nil {
		return x.VolumeGainDb
	}
	return 0
}

func (x *AudioConfig) GetSampleRateHertz() int32 {
	if x != nil {
		return x.SampleRateHertz
	}
	return 0
}

type TtsConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StreamInOff  bool         `protobuf:"varint,1,opt,name=stream_in_off,json=streamInOff,proto3" json:"stream_in_off,omitempty"`    // Whether to turn off input streaming
	StreamOutOff bool         `protobuf:"varint,2,opt,name=stream_out_off,json=streamOutOff,proto3" json:"stream_out_off,omitempty"` // Whether to turn off stream output streaming
	Voice        *VoiceConfig `protobuf:"bytes,3,opt,name=voice,proto3" json:"voice,omitempty"`                                      // Voice-related configuration
	Audio        *AudioConfig `protobuf:"bytes,4,opt,name=audio,proto3" json:"audio,omitempty"`                                      // Audio-related configuration
}

func (x *TtsConfig) Reset() {
	*x = TtsConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_double_stream_tts_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsConfig) ProtoMessage() {}

func (x *TtsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_double_stream_tts_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsConfig.ProtoReflect.Descriptor instead.
func (*TtsConfig) Descriptor() ([]byte, []int) {
	return file_double_stream_tts_proto_rawDescGZIP(), []int{2}
}

func (x *TtsConfig) GetStreamInOff() bool {
	if x != nil {
		return x.StreamInOff
	}
	return false
}

func (x *TtsConfig) GetStreamOutOff() bool {
	if x != nil {
		return x.StreamOutOff
	}
	return false
}

func (x *TtsConfig) GetVoice() *VoiceConfig {
	if x != nil {
		return x.Voice
	}
	return nil
}

func (x *TtsConfig) GetAudio() *AudioConfig {
	if x != nil {
		return x.Audio
	}
	return nil
}

type TtsContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text  string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	IsEnd bool   `protobuf:"varint,2,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
}

func (x *TtsContent) Reset() {
	*x = TtsContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_double_stream_tts_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsContent) ProtoMessage() {}

func (x *TtsContent) ProtoReflect() protoreflect.Message {
	mi := &file_double_stream_tts_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsContent.ProtoReflect.Descriptor instead.
func (*TtsContent) Descriptor() ([]byte, []int) {
	return file_double_stream_tts_proto_rawDescGZIP(), []int{3}
}

func (x *TtsContent) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *TtsContent) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

type Authentication struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"` // secret key
}

func (x *Authentication) Reset() {
	*x = Authentication{}
	if protoimpl.UnsafeEnabled {
		mi := &file_double_stream_tts_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Authentication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Authentication) ProtoMessage() {}

func (x *Authentication) ProtoReflect() protoreflect.Message {
	mi := &file_double_stream_tts_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Authentication.ProtoReflect.Descriptor instead.
func (*Authentication) Descriptor() ([]byte, []int) {
	return file_double_stream_tts_proto_rawDescGZIP(), []int{4}
}

func (x *Authentication) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type TtsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*TtsRequest_Config
	//	*TtsRequest_Content
	Request        isTtsRequest_Request `protobuf_oneof:"request"`
	Authentication *Authentication      `protobuf:"bytes,3,opt,name=authentication,proto3" json:"authentication,omitempty"` // Authentication information
}

func (x *TtsRequest) Reset() {
	*x = TtsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_double_stream_tts_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsRequest) ProtoMessage() {}

func (x *TtsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_double_stream_tts_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsRequest.ProtoReflect.Descriptor instead.
func (*TtsRequest) Descriptor() ([]byte, []int) {
	return file_double_stream_tts_proto_rawDescGZIP(), []int{5}
}

func (m *TtsRequest) GetRequest() isTtsRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *TtsRequest) GetConfig() *TtsConfig {
	if x, ok := x.GetRequest().(*TtsRequest_Config); ok {
		return x.Config
	}
	return nil
}

func (x *TtsRequest) GetContent() *TtsContent {
	if x, ok := x.GetRequest().(*TtsRequest_Content); ok {
		return x.Content
	}
	return nil
}

func (x *TtsRequest) GetAuthentication() *Authentication {
	if x != nil {
		return x.Authentication
	}
	return nil
}

type isTtsRequest_Request interface {
	isTtsRequest_Request()
}

type TtsRequest_Config struct {
	Config *TtsConfig `protobuf:"bytes,1,opt,name=config,proto3,oneof"` // TTS configuration, should only send once, before
}

type TtsRequest_Content struct {
	Content *TtsContent `protobuf:"bytes,2,opt,name=content,proto3,oneof"` // TTS content
}

func (*TtsRequest_Config) isTtsRequest_Request() {}

func (*TtsRequest_Content) isTtsRequest_Request() {}

type TtsResponseMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"` // Request ID for troubleshooting
	// Initial synthesis time of the current sentence, UNIX_TIME (ms)
	SynthesisBeginMs    int64 `protobuf:"varint,2,opt,name=synthesis_begin_ms,json=synthesisBeginMs,proto3" json:"synthesis_begin_ms,omitempty"`
	FirstChunkLatencyMs int64 `protobuf:"varint,3,opt,name=first_chunk_latency_ms,json=firstChunkLatencyMs,proto3" json:"first_chunk_latency_ms,omitempty"` // First block latency
}

func (x *TtsResponseMeta) Reset() {
	*x = TtsResponseMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_double_stream_tts_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsResponseMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsResponseMeta) ProtoMessage() {}

func (x *TtsResponseMeta) ProtoReflect() protoreflect.Message {
	mi := &file_double_stream_tts_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsResponseMeta.ProtoReflect.Descriptor instead.
func (*TtsResponseMeta) Descriptor() ([]byte, []int) {
	return file_double_stream_tts_proto_rawDescGZIP(), []int{6}
}

func (x *TtsResponseMeta) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *TtsResponseMeta) GetSynthesisBeginMs() int64 {
	if x != nil {
		return x.SynthesisBeginMs
	}
	return 0
}

func (x *TtsResponseMeta) GetFirstChunkLatencyMs() int64 {
	if x != nil {
		return x.FirstChunkLatencyMs
	}
	return 0
}

type TtsResponseChunkMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// real-time factor of current block, synthesis time divided
	// by audio length,
	Rtf float32 `protobuf:"fixed32,1,opt,name=rtf,proto3" json:"rtf,omitempty"`
	// Current block synthesis start time, UNIX_TIME (ms)
	ChunkSynthesisBeginMs int64 `protobuf:"varint,2,opt,name=chunk_synthesis_begin_ms,json=chunkSynthesisBeginMs,proto3" json:"chunk_synthesis_begin_ms,omitempty"`
	ChunkId               int32 `protobuf:"varint,3,opt,name=chunk_id,json=chunkId,proto3" json:"chunk_id,omitempty"`               // Which streaming block is currently, starting from 0
	ChunkSizeUs           int64 `protobuf:"varint,4,opt,name=chunk_size_us,json=chunkSizeUs,proto3" json:"chunk_size_us,omitempty"` // The size of the current block (us)
}

func (x *TtsResponseChunkMeta) Reset() {
	*x = TtsResponseChunkMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_double_stream_tts_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsResponseChunkMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsResponseChunkMeta) ProtoMessage() {}

func (x *TtsResponseChunkMeta) ProtoReflect() protoreflect.Message {
	mi := &file_double_stream_tts_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsResponseChunkMeta.ProtoReflect.Descriptor instead.
func (*TtsResponseChunkMeta) Descriptor() ([]byte, []int) {
	return file_double_stream_tts_proto_rawDescGZIP(), []int{7}
}

func (x *TtsResponseChunkMeta) GetRtf() float32 {
	if x != nil {
		return x.Rtf
	}
	return 0
}

func (x *TtsResponseChunkMeta) GetChunkSynthesisBeginMs() int64 {
	if x != nil {
		return x.ChunkSynthesisBeginMs
	}
	return 0
}

func (x *TtsResponseChunkMeta) GetChunkId() int32 {
	if x != nil {
		return x.ChunkId
	}
	return 0
}

func (x *TtsResponseChunkMeta) GetChunkSizeUs() int64 {
	if x != nil {
		return x.ChunkSizeUs
	}
	return 0
}

type TtsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AudioContent []byte `protobuf:"bytes,1,opt,name=audio_content,json=audioContent,proto3" json:"audio_content,omitempty"` // Synthesized audio
	// Status, return OK after success or failure information otherwise
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	IsEnd  bool   `protobuf:"varint,3,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"` // Whether it is the last block of streaming, also set to
	// True in non-streaming situations
	Code int32 `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"` // Return code, 0 for OK, other values ​​for error codes
	// Tts response meta info of every chunk
	TtsResponseChunkMeta *TtsResponseChunkMeta `protobuf:"bytes,5,opt,name=tts_response_chunk_meta,json=ttsResponseChunkMeta,proto3" json:"tts_response_chunk_meta,omitempty"`
	// Tts response meta info, only return along with the first chunk
	TtsResponseMeta *TtsResponseMeta `protobuf:"bytes,6,opt,name=tts_response_meta,json=ttsResponseMeta,proto3" json:"tts_response_meta,omitempty"`
}

func (x *TtsResponse) Reset() {
	*x = TtsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_double_stream_tts_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsResponse) ProtoMessage() {}

func (x *TtsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_double_stream_tts_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsResponse.ProtoReflect.Descriptor instead.
func (*TtsResponse) Descriptor() ([]byte, []int) {
	return file_double_stream_tts_proto_rawDescGZIP(), []int{8}
}

func (x *TtsResponse) GetAudioContent() []byte {
	if x != nil {
		return x.AudioContent
	}
	return nil
}

func (x *TtsResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TtsResponse) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

func (x *TtsResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TtsResponse) GetTtsResponseChunkMeta() *TtsResponseChunkMeta {
	if x != nil {
		return x.TtsResponseChunkMeta
	}
	return nil
}

func (x *TtsResponse) GetTtsResponseMeta() *TtsResponseMeta {
	if x != nil {
		return x.TtsResponseMeta
	}
	return nil
}

type TtsSupportedLangSpeaker struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Languages []string `protobuf:"bytes,1,rep,name=languages,proto3" json:"languages,omitempty"`
	Speakers  []string `protobuf:"bytes,2,rep,name=speakers,proto3" json:"speakers,omitempty"`
}

func (x *TtsSupportedLangSpeaker) Reset() {
	*x = TtsSupportedLangSpeaker{}
	if protoimpl.UnsafeEnabled {
		mi := &file_double_stream_tts_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsSupportedLangSpeaker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsSupportedLangSpeaker) ProtoMessage() {}

func (x *TtsSupportedLangSpeaker) ProtoReflect() protoreflect.Message {
	mi := &file_double_stream_tts_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsSupportedLangSpeaker.ProtoReflect.Descriptor instead.
func (*TtsSupportedLangSpeaker) Descriptor() ([]byte, []int) {
	return file_double_stream_tts_proto_rawDescGZIP(), []int{9}
}

func (x *TtsSupportedLangSpeaker) GetLanguages() []string {
	if x != nil {
		return x.Languages
	}
	return nil
}

func (x *TtsSupportedLangSpeaker) GetSpeakers() []string {
	if x != nil {
		return x.Speakers
	}
	return nil
}

var File_double_stream_tts_proto protoreflect.FileDescriptor

var file_double_stream_tts_proto_rawDesc = []byte{
	0x0a, 0x17, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f,
	0x74, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x69, 0x6f, 0x2e, 0x6e, 0x6f,
	0x70, 0x61, 0x75, 0x73, 0x65, 0x2e, 0x74, 0x74, 0x73, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4d, 0x0a, 0x0b, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0xab, 0x01, 0x0a, 0x0b, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x65,
	0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61,
	0x75, 0x64, 0x69, 0x6f, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x70, 0x65, 0x61, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0c, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x74,
	0x65, 0x12, 0x24, 0x0a, 0x0e, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x69, 0x6e,
	0x5f, 0x64, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x76, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x47, 0x61, 0x69, 0x6e, 0x44, 0x62, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x61, 0x6d, 0x70, 0x6c,
	0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x68, 0x65, 0x72, 0x74, 0x7a, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0f, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x61, 0x74, 0x65, 0x48, 0x65,
	0x72, 0x74, 0x7a, 0x22, 0xbb, 0x01, 0x0a, 0x09, 0x54, 0x74, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x6e, 0x5f, 0x6f,
	0x66, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x49, 0x6e, 0x4f, 0x66, 0x66, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f,
	0x6f, 0x75, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x4f, 0x75, 0x74, 0x4f, 0x66, 0x66, 0x12, 0x31, 0x0a, 0x05, 0x76,
	0x6f, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x69, 0x6f, 0x2e,
	0x6e, 0x6f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x2e, 0x74, 0x74, 0x73, 0x2e, 0x56, 0x6f, 0x69, 0x63,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x31,
	0x0a, 0x05, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x69, 0x6f, 0x2e, 0x6e, 0x6f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x2e, 0x74, 0x74, 0x73, 0x2e, 0x41,
	0x75, 0x64, 0x69, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x61, 0x75, 0x64, 0x69,
	0x6f, 0x22, 0x37, 0x0a, 0x0a, 0x54, 0x74, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x45, 0x6e, 0x64, 0x22, 0x22, 0x0a, 0x0e, 0x41, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0xcc,
	0x01, 0x0a, 0x0a, 0x54, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a,
	0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x69, 0x6f, 0x2e, 0x6e, 0x6f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x2e, 0x74, 0x74, 0x73, 0x2e, 0x54,
	0x74, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x36, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x6f, 0x2e, 0x6e, 0x6f, 0x70, 0x61, 0x75, 0x73, 0x65,
	0x2e, 0x74, 0x74, 0x73, 0x2e, 0x54, 0x74, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x48,
	0x00, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x0e, 0x61, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x69, 0x6f, 0x2e, 0x6e, 0x6f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x2e,
	0x74, 0x74, 0x73, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x93, 0x01,
	0x0a, 0x0f, 0x54, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x2c, 0x0a, 0x12, 0x73, 0x79, 0x6e, 0x74, 0x68, 0x65, 0x73, 0x69, 0x73, 0x5f, 0x62, 0x65,
	0x67, 0x69, 0x6e, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x73, 0x79,
	0x6e, 0x74, 0x68, 0x65, 0x73, 0x69, 0x73, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x4d, 0x73, 0x12, 0x33,
	0x0a, 0x16, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x6c, 0x61,
	0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63,
	0x79, 0x4d, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x14, 0x54, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03,
	0x72, 0x74, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x72, 0x74, 0x66, 0x12, 0x37,
	0x0a, 0x18, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x73, 0x79, 0x6e, 0x74, 0x68, 0x65, 0x73, 0x69,
	0x73, 0x5f, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x15, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x79, 0x6e, 0x74, 0x68, 0x65, 0x73, 0x69, 0x73,
	0x42, 0x65, 0x67, 0x69, 0x6e, 0x4d, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x5f, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x53, 0x69, 0x7a, 0x65, 0x55, 0x73, 0x22, 0x9f, 0x02, 0x0a, 0x0b, 0x54, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x61,
	0x75, 0x64, 0x69, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x45, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x5b,
	0x0a, 0x17, 0x74, 0x74, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x69, 0x6f, 0x2e, 0x6e, 0x6f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x2e, 0x74, 0x74, 0x73,
	0x2e, 0x54, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x68, 0x75, 0x6e,
	0x6b, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x14, 0x74, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x4b, 0x0a, 0x11, 0x74,
	0x74, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x69, 0x6f, 0x2e, 0x6e, 0x6f, 0x70, 0x61,
	0x75, 0x73, 0x65, 0x2e, 0x74, 0x74, 0x73, 0x2e, 0x54, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0f, 0x74, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x53, 0x0a, 0x17, 0x54, 0x74, 0x73, 0x53,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x4c, 0x61, 0x6e, 0x67, 0x53, 0x70, 0x65, 0x61,
	0x6b, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x73, 0x32, 0xac, 0x01,
	0x0a, 0x03, 0x54, 0x74, 0x73, 0x12, 0x4a, 0x0a, 0x09, 0x53, 0x79, 0x6e, 0x74, 0x68, 0x65, 0x73,
	0x69, 0x73, 0x12, 0x1a, 0x2e, 0x69, 0x6f, 0x2e, 0x6e, 0x6f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x2e,
	0x74, 0x74, 0x73, 0x2e, 0x54, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b,
	0x2e, 0x69, 0x6f, 0x2e, 0x6e, 0x6f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x2e, 0x74, 0x74, 0x73, 0x2e,
	0x54, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x28, 0x01, 0x30,
	0x01, 0x12, 0x59, 0x0a, 0x14, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x4c, 0x61,
	0x6e, 0x67, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x1a, 0x27, 0x2e, 0x69, 0x6f, 0x2e, 0x6e, 0x6f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x2e, 0x74,
	0x74, 0x73, 0x2e, 0x54, 0x74, 0x73, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x4c,
	0x61, 0x6e, 0x67, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x22, 0x00, 0x42, 0x11, 0x5a, 0x0f,
	0x2e, 0x2f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_double_stream_tts_proto_rawDescOnce sync.Once
	file_double_stream_tts_proto_rawDescData = file_double_stream_tts_proto_rawDesc
)

func file_double_stream_tts_proto_rawDescGZIP() []byte {
	file_double_stream_tts_proto_rawDescOnce.Do(func() {
		file_double_stream_tts_proto_rawDescData = protoimpl.X.CompressGZIP(file_double_stream_tts_proto_rawDescData)
	})
	return file_double_stream_tts_proto_rawDescData
}

var file_double_stream_tts_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_double_stream_tts_proto_goTypes = []interface{}{
	(*VoiceConfig)(nil),             // 0: io.nopause.tts.VoiceConfig
	(*AudioConfig)(nil),             // 1: io.nopause.tts.AudioConfig
	(*TtsConfig)(nil),               // 2: io.nopause.tts.TtsConfig
	(*TtsContent)(nil),              // 3: io.nopause.tts.TtsContent
	(*Authentication)(nil),          // 4: io.nopause.tts.Authentication
	(*TtsRequest)(nil),              // 5: io.nopause.tts.TtsRequest
	(*TtsResponseMeta)(nil),         // 6: io.nopause.tts.TtsResponseMeta
	(*TtsResponseChunkMeta)(nil),    // 7: io.nopause.tts.TtsResponseChunkMeta
	(*TtsResponse)(nil),             // 8: io.nopause.tts.TtsResponse
	(*TtsSupportedLangSpeaker)(nil), // 9: io.nopause.tts.TtsSupportedLangSpeaker
	(*emptypb.Empty)(nil),           // 10: google.protobuf.Empty
}
var file_double_stream_tts_proto_depIdxs = []int32{
	0,  // 0: io.nopause.tts.TtsConfig.voice:type_name -> io.nopause.tts.VoiceConfig
	1,  // 1: io.nopause.tts.TtsConfig.audio:type_name -> io.nopause.tts.AudioConfig
	2,  // 2: io.nopause.tts.TtsRequest.config:type_name -> io.nopause.tts.TtsConfig
	3,  // 3: io.nopause.tts.TtsRequest.content:type_name -> io.nopause.tts.TtsContent
	4,  // 4: io.nopause.tts.TtsRequest.authentication:type_name -> io.nopause.tts.Authentication
	7,  // 5: io.nopause.tts.TtsResponse.tts_response_chunk_meta:type_name -> io.nopause.tts.TtsResponseChunkMeta
	6,  // 6: io.nopause.tts.TtsResponse.tts_response_meta:type_name -> io.nopause.tts.TtsResponseMeta
	5,  // 7: io.nopause.tts.Tts.Synthesis:input_type -> io.nopause.tts.TtsRequest
	10, // 8: io.nopause.tts.Tts.SupportedLangSpeaker:input_type -> google.protobuf.Empty
	8,  // 9: io.nopause.tts.Tts.Synthesis:output_type -> io.nopause.tts.TtsResponse
	9,  // 10: io.nopause.tts.Tts.SupportedLangSpeaker:output_type -> io.nopause.tts.TtsSupportedLangSpeaker
	9,  // [9:11] is the sub-list for method output_type
	7,  // [7:9] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_double_stream_tts_proto_init() }
func file_double_stream_tts_proto_init() {
	if File_double_stream_tts_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_double_stream_tts_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoiceConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_double_stream_tts_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudioConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_double_stream_tts_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_double_stream_tts_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_double_stream_tts_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Authentication); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_double_stream_tts_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_double_stream_tts_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsResponseMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_double_stream_tts_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsResponseChunkMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_double_stream_tts_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_double_stream_tts_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsSupportedLangSpeaker); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_double_stream_tts_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*TtsRequest_Config)(nil),
		(*TtsRequest_Content)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_double_stream_tts_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_double_stream_tts_proto_goTypes,
		DependencyIndexes: file_double_stream_tts_proto_depIdxs,
		MessageInfos:      file_double_stream_tts_proto_msgTypes,
	}.Build()
	File_double_stream_tts_proto = out.File
	file_double_stream_tts_proto_rawDesc = nil
	file_double_stream_tts_proto_goTypes = nil
	file_double_stream_tts_proto_depIdxs = nil
}
