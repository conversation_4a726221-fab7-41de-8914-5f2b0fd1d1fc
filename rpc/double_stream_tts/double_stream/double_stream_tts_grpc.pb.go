// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.4
// source: double_stream_tts.proto

package double_stream

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TtsClient is the client API for Tts service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TtsClient interface {
	Synthesis(ctx context.Context, opts ...grpc.CallOption) (Tts_SynthesisClient, error)
	// get supported languages and speakers
	SupportedLangSpeaker(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*TtsSupportedLangSpeaker, error)
}

type ttsClient struct {
	cc grpc.ClientConnInterface
}

func NewTtsClient(cc grpc.ClientConnInterface) TtsClient {
	return &ttsClient{cc}
}

func (c *ttsClient) Synthesis(ctx context.Context, opts ...grpc.CallOption) (Tts_SynthesisClient, error) {
	stream, err := c.cc.NewStream(ctx, &Tts_ServiceDesc.Streams[0], "/io.nopause.tts.Tts/Synthesis", opts...)
	if err != nil {
		return nil, err
	}
	x := &ttsSynthesisClient{stream}
	return x, nil
}

type Tts_SynthesisClient interface {
	Send(*TtsRequest) error
	Recv() (*TtsResponse, error)
	grpc.ClientStream
}

type ttsSynthesisClient struct {
	grpc.ClientStream
}

func (x *ttsSynthesisClient) Send(m *TtsRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *ttsSynthesisClient) Recv() (*TtsResponse, error) {
	m := new(TtsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *ttsClient) SupportedLangSpeaker(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*TtsSupportedLangSpeaker, error) {
	out := new(TtsSupportedLangSpeaker)
	err := c.cc.Invoke(ctx, "/io.nopause.tts.Tts/SupportedLangSpeaker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TtsServer is the server API for Tts service.
// All implementations must embed UnimplementedTtsServer
// for forward compatibility
type TtsServer interface {
	Synthesis(Tts_SynthesisServer) error
	// get supported languages and speakers
	SupportedLangSpeaker(context.Context, *emptypb.Empty) (*TtsSupportedLangSpeaker, error)
	mustEmbedUnimplementedTtsServer()
}

// UnimplementedTtsServer must be embedded to have forward compatible implementations.
type UnimplementedTtsServer struct {
}

func (UnimplementedTtsServer) Synthesis(Tts_SynthesisServer) error {
	return status.Errorf(codes.Unimplemented, "method Synthesis not implemented")
}
func (UnimplementedTtsServer) SupportedLangSpeaker(context.Context, *emptypb.Empty) (*TtsSupportedLangSpeaker, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SupportedLangSpeaker not implemented")
}
func (UnimplementedTtsServer) mustEmbedUnimplementedTtsServer() {}

// UnsafeTtsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TtsServer will
// result in compilation errors.
type UnsafeTtsServer interface {
	mustEmbedUnimplementedTtsServer()
}

func RegisterTtsServer(s grpc.ServiceRegistrar, srv TtsServer) {
	s.RegisterService(&Tts_ServiceDesc, srv)
}

func _Tts_Synthesis_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(TtsServer).Synthesis(&ttsSynthesisServer{stream})
}

type Tts_SynthesisServer interface {
	Send(*TtsResponse) error
	Recv() (*TtsRequest, error)
	grpc.ServerStream
}

type ttsSynthesisServer struct {
	grpc.ServerStream
}

func (x *ttsSynthesisServer) Send(m *TtsResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *ttsSynthesisServer) Recv() (*TtsRequest, error) {
	m := new(TtsRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Tts_SupportedLangSpeaker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtsServer).SupportedLangSpeaker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/io.nopause.tts.Tts/SupportedLangSpeaker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtsServer).SupportedLangSpeaker(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// Tts_ServiceDesc is the grpc.ServiceDesc for Tts service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Tts_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "io.nopause.tts.Tts",
	HandlerType: (*TtsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SupportedLangSpeaker",
			Handler:    _Tts_SupportedLangSpeaker_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Synthesis",
			Handler:       _Tts_Synthesis_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "double_stream_tts.proto",
}
