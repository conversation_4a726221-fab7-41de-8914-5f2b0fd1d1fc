// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.19.4
// source: tts.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VoiceConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageCode string `protobuf:"bytes,1,opt,name=language_code,json=languageCode,proto3" json:"language_code,omitempty"` // 语种名 id-ID
	Name         string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                     // 音色名, id-ID-angelina
	SsmlGender   string `protobuf:"bytes,3,opt,name=ssml_gender,json=ssmlGender,proto3" json:"ssml_gender,omitempty"`       // 与谷歌的ssmlGender相同
}

func (x *VoiceConfig) Reset() {
	*x = VoiceConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tts_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoiceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoiceConfig) ProtoMessage() {}

func (x *VoiceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_tts_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoiceConfig.ProtoReflect.Descriptor instead.
func (*VoiceConfig) Descriptor() ([]byte, []int) {
	return file_tts_proto_rawDescGZIP(), []int{0}
}

func (x *VoiceConfig) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *VoiceConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VoiceConfig) GetSsmlGender() string {
	if x != nil {
		return x.SsmlGender
	}
	return ""
}

type AudioConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AudioEncoding    string  `protobuf:"bytes,1,opt,name=audio_encoding,json=audioEncoding,proto3" json:"audio_encoding,omitempty"`            // 音频返回格式，例如wav
	SpeakingRate     float32 `protobuf:"fixed32,2,opt,name=speaking_rate,json=speakingRate,proto3" json:"speaking_rate,omitempty"`             // 语速，1.0不变，必须要传因为默认是0
	Pitch            float32 `protobuf:"fixed32,3,opt,name=pitch,proto3" json:"pitch,omitempty"`                                               // 音调，0不变，越大音调越高
	VolumeGainDb     float32 `protobuf:"fixed32,4,opt,name=volume_gain_db,json=volumeGainDb,proto3" json:"volume_gain_db,omitempty"`           // 音量增益，为0时输出原始音量，数值越大音量越大
	SampleRateHertz  int32   `protobuf:"varint,5,opt,name=sample_rate_hertz,json=sampleRateHertz,proto3" json:"sample_rate_hertz,omitempty"`   // 采样率
	EffectsProfileId string  `protobuf:"bytes,6,opt,name=effects_profile_id,json=effectsProfileId,proto3" json:"effects_profile_id,omitempty"` // 音效
}

func (x *AudioConfig) Reset() {
	*x = AudioConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tts_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudioConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioConfig) ProtoMessage() {}

func (x *AudioConfig) ProtoReflect() protoreflect.Message {
	mi := &file_tts_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioConfig.ProtoReflect.Descriptor instead.
func (*AudioConfig) Descriptor() ([]byte, []int) {
	return file_tts_proto_rawDescGZIP(), []int{1}
}

func (x *AudioConfig) GetAudioEncoding() string {
	if x != nil {
		return x.AudioEncoding
	}
	return ""
}

func (x *AudioConfig) GetSpeakingRate() float32 {
	if x != nil {
		return x.SpeakingRate
	}
	return 0
}

func (x *AudioConfig) GetPitch() float32 {
	if x != nil {
		return x.Pitch
	}
	return 0
}

func (x *AudioConfig) GetVolumeGainDb() float32 {
	if x != nil {
		return x.VolumeGainDb
	}
	return 0
}

func (x *AudioConfig) GetSampleRateHertz() int32 {
	if x != nil {
		return x.SampleRateHertz
	}
	return 0
}

func (x *AudioConfig) GetEffectsProfileId() string {
	if x != nil {
		return x.EffectsProfileId
	}
	return ""
}

type TtsConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId    string       `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`             // 请求ID，用于定位问题
	Voice        *VoiceConfig `protobuf:"bytes,2,opt,name=voice,proto3" json:"voice,omitempty"`                                      // 音色的相关配置
	Stream       bool         `protobuf:"varint,3,opt,name=stream,proto3" json:"stream,omitempty"`                                   // 是否流式生成
	Audio        *AudioConfig `protobuf:"bytes,4,opt,name=audio,proto3" json:"audio,omitempty"`                                      // 音频的相关配置
	AudioChunkUs int64        `protobuf:"varint,5,opt,name=audio_chunk_us,json=audioChunkUs,proto3" json:"audio_chunk_us,omitempty"` // 期待的流式返回块的大小，stream为True时有效
}

func (x *TtsConfig) Reset() {
	*x = TtsConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tts_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsConfig) ProtoMessage() {}

func (x *TtsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_tts_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsConfig.ProtoReflect.Descriptor instead.
func (*TtsConfig) Descriptor() ([]byte, []int) {
	return file_tts_proto_rawDescGZIP(), []int{2}
}

func (x *TtsConfig) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *TtsConfig) GetVoice() *VoiceConfig {
	if x != nil {
		return x.Voice
	}
	return nil
}

func (x *TtsConfig) GetStream() bool {
	if x != nil {
		return x.Stream
	}
	return false
}

func (x *TtsConfig) GetAudio() *AudioConfig {
	if x != nil {
		return x.Audio
	}
	return nil
}

func (x *TtsConfig) GetAudioChunkUs() int64 {
	if x != nil {
		return x.AudioChunkUs
	}
	return 0
}

type TtsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *TtsConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"` // tts的配置
	Text   string     `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`     // 文本，有前后<speak>则认为输入是SSML
}

func (x *TtsRequest) Reset() {
	*x = TtsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tts_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsRequest) ProtoMessage() {}

func (x *TtsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tts_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsRequest.ProtoReflect.Descriptor instead.
func (*TtsRequest) Descriptor() ([]byte, []int) {
	return file_tts_proto_rawDescGZIP(), []int{3}
}

func (x *TtsRequest) GetConfig() *TtsConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *TtsRequest) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type TtsRequestForConcat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *TtsConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"` // tts的配置
	// 待合成文本，支持ssml格式，如果是ssml格式，必须以<speak>开始，以</speak>结尾
	Text []string `protobuf:"bytes,2,rep,name=text,proto3" json:"text,omitempty"` // 待合成文本
	// 音频内容的参考文本，可选，如果有，和reference_audio_contents一一对应
	// 支持ssml格式，如果是ssml格式，每个文本必须以<speak>开始，以</speak>结尾
	RefText []string `protobuf:"bytes,3,rep,name=ref_text,json=refText,proto3" json:"ref_text,omitempty"`
	// 拼接的参考音频，必须
	// wav格式的数据(不包含wav文件头), 8k, 16bit, 小端
	ReferenceAudioContents [][]byte `protobuf:"bytes,4,rep,name=reference_audio_contents,json=referenceAudioContents,proto3" json:"reference_audio_contents,omitempty"`
	// 拼接的参考音频在整句中的位置, eg: [1, 0, 1]代表拼接的顺序为[音频, TTS, 音频]
	TextAndAudioPositions []bool `protobuf:"varint,5,rep,packed,name=text_and_audio_positions,json=textAndAudioPositions,proto3" json:"text_and_audio_positions,omitempty"`
}

func (x *TtsRequestForConcat) Reset() {
	*x = TtsRequestForConcat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tts_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsRequestForConcat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsRequestForConcat) ProtoMessage() {}

func (x *TtsRequestForConcat) ProtoReflect() protoreflect.Message {
	mi := &file_tts_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsRequestForConcat.ProtoReflect.Descriptor instead.
func (*TtsRequestForConcat) Descriptor() ([]byte, []int) {
	return file_tts_proto_rawDescGZIP(), []int{4}
}

func (x *TtsRequestForConcat) GetConfig() *TtsConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *TtsRequestForConcat) GetText() []string {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *TtsRequestForConcat) GetRefText() []string {
	if x != nil {
		return x.RefText
	}
	return nil
}

func (x *TtsRequestForConcat) GetReferenceAudioContents() [][]byte {
	if x != nil {
		return x.ReferenceAudioContents
	}
	return nil
}

func (x *TtsRequestForConcat) GetTextAndAudioPositions() []bool {
	if x != nil {
		return x.TextAndAudioPositions
	}
	return nil
}

type TtsRequestForEdit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *TtsConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"` // tts的配置
	Text   []string   `protobuf:"bytes,2,rep,name=text,proto3" json:"text,omitempty"`     // 文本
	// 音频内容的参考文本，可选，如果有，和reference_audio_contents一一对应
	RefText               []string `protobuf:"bytes,3,rep,name=ref_text,json=refText,proto3" json:"ref_text,omitempty"`
	ReferenceAudioContent []byte   `protobuf:"bytes,4,opt,name=reference_audio_content,json=referenceAudioContent,proto3" json:"reference_audio_content,omitempty"` // 待修改的参考音频
	// 拼接的TTS音频在整句中的位置
	EditPositions []*TtsRequestForEdit_BeginEndForEdit `protobuf:"bytes,5,rep,name=edit_positions,json=editPositions,proto3" json:"edit_positions,omitempty"`
}

func (x *TtsRequestForEdit) Reset() {
	*x = TtsRequestForEdit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tts_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsRequestForEdit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsRequestForEdit) ProtoMessage() {}

func (x *TtsRequestForEdit) ProtoReflect() protoreflect.Message {
	mi := &file_tts_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsRequestForEdit.ProtoReflect.Descriptor instead.
func (*TtsRequestForEdit) Descriptor() ([]byte, []int) {
	return file_tts_proto_rawDescGZIP(), []int{5}
}

func (x *TtsRequestForEdit) GetConfig() *TtsConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *TtsRequestForEdit) GetText() []string {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *TtsRequestForEdit) GetRefText() []string {
	if x != nil {
		return x.RefText
	}
	return nil
}

func (x *TtsRequestForEdit) GetReferenceAudioContent() []byte {
	if x != nil {
		return x.ReferenceAudioContent
	}
	return nil
}

func (x *TtsRequestForEdit) GetEditPositions() []*TtsRequestForEdit_BeginEndForEdit {
	if x != nil {
		return x.EditPositions
	}
	return nil
}

type FrontendResults struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prosodies []string                `protobuf:"bytes,1,rep,name=prosodies,proto3" json:"prosodies,omitempty"` // 每个词的韵律标签
	Words     []*FrontendResults_Word `protobuf:"bytes,2,rep,name=words,proto3" json:"words,omitempty"`         // 词的列表
}

func (x *FrontendResults) Reset() {
	*x = FrontendResults{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tts_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FrontendResults) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrontendResults) ProtoMessage() {}

func (x *FrontendResults) ProtoReflect() protoreflect.Message {
	mi := &file_tts_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrontendResults.ProtoReflect.Descriptor instead.
func (*FrontendResults) Descriptor() ([]byte, []int) {
	return file_tts_proto_rawDescGZIP(), []int{6}
}

func (x *FrontendResults) GetProsodies() []string {
	if x != nil {
		return x.Prosodies
	}
	return nil
}

func (x *FrontendResults) GetWords() []*FrontendResults_Word {
	if x != nil {
		return x.Words
	}
	return nil
}

type TtsResponseMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SynthesisBeginMs    int64            `protobuf:"varint,1,opt,name=synthesis_begin_ms,json=synthesisBeginMs,proto3" json:"synthesis_begin_ms,omitempty"`            // 当前句子的初始合成时间,UNIX_TIME(ms)
	FirstChunkLatencyMs int64            `protobuf:"varint,2,opt,name=first_chunk_latency_ms,json=firstChunkLatencyMs,proto3" json:"first_chunk_latency_ms,omitempty"` // 首块延迟
	FrontendCostMs      int64            `protobuf:"varint,3,opt,name=frontend_cost_ms,json=frontendCostMs,proto3" json:"frontend_cost_ms,omitempty"`                  // 前端耗时
	FrontendResults     *FrontendResults `protobuf:"bytes,4,opt,name=frontend_results,json=frontendResults,proto3" json:"frontend_results,omitempty"`                  // 前端的结果
	ActualAudioConfig   *AudioConfig     `protobuf:"bytes,5,opt,name=actual_audio_config,json=actualAudioConfig,proto3" json:"actual_audio_config,omitempty"`          // 实际使用的音频配置
}

func (x *TtsResponseMeta) Reset() {
	*x = TtsResponseMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tts_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsResponseMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsResponseMeta) ProtoMessage() {}

func (x *TtsResponseMeta) ProtoReflect() protoreflect.Message {
	mi := &file_tts_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsResponseMeta.ProtoReflect.Descriptor instead.
func (*TtsResponseMeta) Descriptor() ([]byte, []int) {
	return file_tts_proto_rawDescGZIP(), []int{7}
}

func (x *TtsResponseMeta) GetSynthesisBeginMs() int64 {
	if x != nil {
		return x.SynthesisBeginMs
	}
	return 0
}

func (x *TtsResponseMeta) GetFirstChunkLatencyMs() int64 {
	if x != nil {
		return x.FirstChunkLatencyMs
	}
	return 0
}

func (x *TtsResponseMeta) GetFrontendCostMs() int64 {
	if x != nil {
		return x.FrontendCostMs
	}
	return 0
}

func (x *TtsResponseMeta) GetFrontendResults() *FrontendResults {
	if x != nil {
		return x.FrontendResults
	}
	return nil
}

func (x *TtsResponseMeta) GetActualAudioConfig() *AudioConfig {
	if x != nil {
		return x.ActualAudioConfig
	}
	return nil
}

type TtsResponseChunkMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rtf                   float32 `protobuf:"fixed32,1,opt,name=rtf,proto3" json:"rtf,omitempty"`                                                                     // 当前块实时率，合成耗时除以音频的长度，
	ChunkSynthesisBeginMs int64   `protobuf:"varint,2,opt,name=chunk_synthesis_begin_ms,json=chunkSynthesisBeginMs,proto3" json:"chunk_synthesis_begin_ms,omitempty"` // 当前块的开始合成时间, UNIX_TIME(ms)
	ChunkId               int32   `protobuf:"varint,3,opt,name=chunk_id,json=chunkId,proto3" json:"chunk_id,omitempty"`                                               // 当前是流式的第几块，从0开始
	ChunkSizeUs           int64   `protobuf:"varint,4,opt,name=chunk_size_us,json=chunkSizeUs,proto3" json:"chunk_size_us,omitempty"`                                 // 当前块的大小(us)
}

func (x *TtsResponseChunkMeta) Reset() {
	*x = TtsResponseChunkMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tts_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsResponseChunkMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsResponseChunkMeta) ProtoMessage() {}

func (x *TtsResponseChunkMeta) ProtoReflect() protoreflect.Message {
	mi := &file_tts_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsResponseChunkMeta.ProtoReflect.Descriptor instead.
func (*TtsResponseChunkMeta) Descriptor() ([]byte, []int) {
	return file_tts_proto_rawDescGZIP(), []int{8}
}

func (x *TtsResponseChunkMeta) GetRtf() float32 {
	if x != nil {
		return x.Rtf
	}
	return 0
}

func (x *TtsResponseChunkMeta) GetChunkSynthesisBeginMs() int64 {
	if x != nil {
		return x.ChunkSynthesisBeginMs
	}
	return 0
}

func (x *TtsResponseChunkMeta) GetChunkId() int32 {
	if x != nil {
		return x.ChunkId
	}
	return 0
}

func (x *TtsResponseChunkMeta) GetChunkSizeUs() int64 {
	if x != nil {
		return x.ChunkSizeUs
	}
	return 0
}

type TtsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AudioContent         []byte                `protobuf:"bytes,1,opt,name=audio_content,json=audioContent,proto3" json:"audio_content,omitempty"`                             // 合成的音频
	Status               string                `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`                                                             // 状态，失败时给出失败信息，成功后返回OK
	IsEnd                bool                  `protobuf:"varint,3,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`                                                 // 是否是流式的最后一块，非流式情况也设置为True
	Code                 int32                 `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`                                                                // 返回的code，code为0时，有音频，否则失败
	TtsResponseChunkMeta *TtsResponseChunkMeta `protobuf:"bytes,5,opt,name=tts_response_chunk_meta,json=ttsResponseChunkMeta,proto3" json:"tts_response_chunk_meta,omitempty"` // 返回的块的状态特征
	TtsResponseMeta      *TtsResponseMeta      `protobuf:"bytes,6,opt,name=tts_response_meta,json=ttsResponseMeta,proto3" json:"tts_response_meta,omitempty"`                  // 返回的每句的状态特征，伴随第一块音频返回
}

func (x *TtsResponse) Reset() {
	*x = TtsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tts_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsResponse) ProtoMessage() {}

func (x *TtsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tts_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsResponse.ProtoReflect.Descriptor instead.
func (*TtsResponse) Descriptor() ([]byte, []int) {
	return file_tts_proto_rawDescGZIP(), []int{9}
}

func (x *TtsResponse) GetAudioContent() []byte {
	if x != nil {
		return x.AudioContent
	}
	return nil
}

func (x *TtsResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TtsResponse) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

func (x *TtsResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TtsResponse) GetTtsResponseChunkMeta() *TtsResponseChunkMeta {
	if x != nil {
		return x.TtsResponseChunkMeta
	}
	return nil
}

func (x *TtsResponse) GetTtsResponseMeta() *TtsResponseMeta {
	if x != nil {
		return x.TtsResponseMeta
	}
	return nil
}

type TtsRequestForEdit_BeginEndForEdit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [begin_ms, end_ms)
	BeginMs int64 `protobuf:"varint,1,opt,name=begin_ms,json=beginMs,proto3" json:"begin_ms,omitempty"` // 待修改部分开始时间
	EndMs   int64 `protobuf:"varint,2,opt,name=end_ms,json=endMs,proto3" json:"end_ms,omitempty"`       // 待修改部分结束时间
}

func (x *TtsRequestForEdit_BeginEndForEdit) Reset() {
	*x = TtsRequestForEdit_BeginEndForEdit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tts_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsRequestForEdit_BeginEndForEdit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsRequestForEdit_BeginEndForEdit) ProtoMessage() {}

func (x *TtsRequestForEdit_BeginEndForEdit) ProtoReflect() protoreflect.Message {
	mi := &file_tts_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsRequestForEdit_BeginEndForEdit.ProtoReflect.Descriptor instead.
func (*TtsRequestForEdit_BeginEndForEdit) Descriptor() ([]byte, []int) {
	return file_tts_proto_rawDescGZIP(), []int{5, 0}
}

func (x *TtsRequestForEdit_BeginEndForEdit) GetBeginMs() int64 {
	if x != nil {
		return x.BeginMs
	}
	return 0
}

func (x *TtsRequestForEdit_BeginEndForEdit) GetEndMs() int64 {
	if x != nil {
		return x.EndMs
	}
	return 0
}

type FrontendResults_Word struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Word         string   `protobuf:"bytes,1,opt,name=word,proto3" json:"word,omitempty"`                                               // 词
	Phonemes     []string `protobuf:"bytes,2,rep,name=phonemes,proto3" json:"phonemes,omitempty"`                                       // 词对应的音素列表
	PhonemeEndUs []int64  `protobuf:"varint,3,rep,packed,name=phoneme_end_us,json=phonemeEndUs,proto3" json:"phoneme_end_us,omitempty"` // 每个音素的时长，首尾相接
}

func (x *FrontendResults_Word) Reset() {
	*x = FrontendResults_Word{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tts_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FrontendResults_Word) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrontendResults_Word) ProtoMessage() {}

func (x *FrontendResults_Word) ProtoReflect() protoreflect.Message {
	mi := &file_tts_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrontendResults_Word.ProtoReflect.Descriptor instead.
func (*FrontendResults_Word) Descriptor() ([]byte, []int) {
	return file_tts_proto_rawDescGZIP(), []int{6, 0}
}

func (x *FrontendResults_Word) GetWord() string {
	if x != nil {
		return x.Word
	}
	return ""
}

func (x *FrontendResults_Word) GetPhonemes() []string {
	if x != nil {
		return x.Phonemes
	}
	return nil
}

func (x *FrontendResults_Word) GetPhonemeEndUs() []int64 {
	if x != nil {
		return x.PhonemeEndUs
	}
	return nil
}

var File_tts_proto protoreflect.FileDescriptor

var file_tts_proto_rawDesc = []byte{
	0x0a, 0x09, 0x74, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x67, 0x0a, 0x0b, 0x56,
	0x6f, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x73, 0x6d, 0x6c, 0x5f, 0x67, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x73, 0x6d, 0x6c, 0x47, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x22, 0xef, 0x01, 0x0a, 0x0b, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x65, 0x6e,
	0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x75,
	0x64, 0x69, 0x6f, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x73,
	0x70, 0x65, 0x61, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0c, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x74, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x69, 0x74, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x05, 0x70, 0x69, 0x74, 0x63, 0x68, 0x12, 0x24, 0x0a, 0x0e, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x5f, 0x67, 0x61, 0x69, 0x6e, 0x5f, 0x64, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x47, 0x61, 0x69, 0x6e, 0x44, 0x62, 0x12, 0x2a, 0x0a, 0x11,
	0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x68, 0x65, 0x72, 0x74,
	0x7a, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52,
	0x61, 0x74, 0x65, 0x48, 0x65, 0x72, 0x74, 0x7a, 0x12, 0x2c, 0x0a, 0x12, 0x65, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x73, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22, 0xb0, 0x01, 0x0a, 0x09, 0x54, 0x74, 0x73, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x05, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x05, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12,
	0x22, 0x0a, 0x05, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c,
	0x2e, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x61, 0x75,
	0x64, 0x69, 0x6f, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x5f, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x75, 0x64,
	0x69, 0x6f, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x55, 0x73, 0x22, 0x44, 0x0a, 0x0a, 0x54, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x54, 0x74, 0x73, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x22,
	0xdb, 0x01, 0x0a, 0x13, 0x54, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x6f,
	0x72, 0x43, 0x6f, 0x6e, 0x63, 0x61, 0x74, 0x12, 0x22, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x54, 0x74, 0x73, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x72, 0x65, 0x66, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x72, 0x65, 0x66, 0x54, 0x65, 0x78, 0x74, 0x12, 0x38, 0x0a, 0x18, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x16, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x6e, 0x64,
	0x5f, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x08, 0x52, 0x15, 0x74, 0x65, 0x78, 0x74, 0x41, 0x6e, 0x64, 0x41,
	0x75, 0x64, 0x69, 0x6f, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xae, 0x02,
	0x0a, 0x11, 0x54, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x45,
	0x64, 0x69, 0x74, 0x12, 0x22, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x54, 0x74, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x72,
	0x65, 0x66, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x72,
	0x65, 0x66, 0x54, 0x65, 0x78, 0x74, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x15, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x49,
	0x0a, 0x0e, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x54, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x45, 0x64, 0x69, 0x74, 0x2e, 0x42, 0x65, 0x67, 0x69, 0x6e,
	0x45, 0x6e, 0x64, 0x46, 0x6f, 0x72, 0x45, 0x64, 0x69, 0x74, 0x52, 0x0d, 0x65, 0x64, 0x69, 0x74,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x43, 0x0a, 0x0f, 0x42, 0x65, 0x67,
	0x69, 0x6e, 0x45, 0x6e, 0x64, 0x46, 0x6f, 0x72, 0x45, 0x64, 0x69, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x62, 0x65, 0x67, 0x69, 0x6e, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x62, 0x65, 0x67, 0x69, 0x6e, 0x4d, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x6e, 0x64, 0x5f, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x22, 0xba,
	0x01, 0x0a, 0x0f, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x73, 0x6f, 0x64, 0x69, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x73, 0x6f, 0x64, 0x69, 0x65, 0x73,
	0x12, 0x2b, 0x0a, 0x05, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x2e, 0x57, 0x6f, 0x72, 0x64, 0x52, 0x05, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x1a, 0x5c, 0x0a,
	0x04, 0x57, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x6d, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x6d, 0x65,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x75, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x6d, 0x65, 0x45, 0x6e, 0x64, 0x55, 0x73, 0x22, 0x99, 0x02, 0x0a, 0x0f,
	0x54, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12,
	0x2c, 0x0a, 0x12, 0x73, 0x79, 0x6e, 0x74, 0x68, 0x65, 0x73, 0x69, 0x73, 0x5f, 0x62, 0x65, 0x67,
	0x69, 0x6e, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x73, 0x79, 0x6e,
	0x74, 0x68, 0x65, 0x73, 0x69, 0x73, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x4d, 0x73, 0x12, 0x33, 0x0a,
	0x16, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x6c, 0x61, 0x74,
	0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79,
	0x4d, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x63,
	0x6f, 0x73, 0x74, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x73, 0x74, 0x4d, 0x73, 0x12, 0x3b, 0x0a, 0x10,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x52, 0x0f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x3c, 0x0a, 0x13, 0x61, 0x63, 0x74,
	0x75, 0x61, 0x6c, 0x5f, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x11, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x41, 0x75, 0x64, 0x69,
	0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xa0, 0x01, 0x0a, 0x14, 0x54, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4d, 0x65, 0x74, 0x61,
	0x12, 0x10, 0x0a, 0x03, 0x72, 0x74, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x72,
	0x74, 0x66, 0x12, 0x37, 0x0a, 0x18, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x73, 0x79, 0x6e, 0x74,
	0x68, 0x65, 0x73, 0x69, 0x73, 0x5f, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x5f, 0x6d, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x79, 0x6e, 0x74, 0x68,
	0x65, 0x73, 0x69, 0x73, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x4d, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x5f, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x55, 0x73, 0x22, 0x81, 0x02, 0x0a, 0x0b, 0x54,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x75,
	0x64, 0x69, 0x6f, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0c, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x65, 0x6e,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x45, 0x6e, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x4c, 0x0a, 0x17, 0x74, 0x74, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x54, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x14, 0x74, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4d, 0x65, 0x74, 0x61,
	0x12, 0x3c, 0x0a, 0x11, 0x74, 0x74, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x54, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0f, 0x74,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x32, 0xd0,
	0x01, 0x0a, 0x03, 0x54, 0x74, 0x73, 0x12, 0x2b, 0x0a, 0x08, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x45,
	0x6e, 0x64, 0x12, 0x0b, 0x2e, 0x54, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x10, 0x2e, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x22, 0x00, 0x12, 0x2a, 0x0a, 0x09, 0x53, 0x79, 0x6e, 0x74, 0x68, 0x65, 0x73, 0x69, 0x73,
	0x12, 0x0b, 0x2e, 0x54, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0c, 0x2e,
	0x54, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x30, 0x01, 0x12,
	0x39, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x63, 0x61, 0x74, 0x53, 0x79, 0x6e, 0x74, 0x68, 0x65, 0x73,
	0x69, 0x73, 0x12, 0x14, 0x2e, 0x54, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46,
	0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x63, 0x61, 0x74, 0x1a, 0x0c, 0x2e, 0x54, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x30, 0x01, 0x12, 0x35, 0x0a, 0x0d, 0x45, 0x64,
	0x69, 0x74, 0x53, 0x79, 0x6e, 0x74, 0x68, 0x65, 0x73, 0x69, 0x73, 0x12, 0x12, 0x2e, 0x54, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x45, 0x64, 0x69, 0x74, 0x1a,
	0x0c, 0x2e, 0x54, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x30,
	0x01, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_tts_proto_rawDescOnce sync.Once
	file_tts_proto_rawDescData = file_tts_proto_rawDesc
)

func file_tts_proto_rawDescGZIP() []byte {
	file_tts_proto_rawDescOnce.Do(func() {
		file_tts_proto_rawDescData = protoimpl.X.CompressGZIP(file_tts_proto_rawDescData)
	})
	return file_tts_proto_rawDescData
}

var file_tts_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_tts_proto_goTypes = []interface{}{
	(*VoiceConfig)(nil),                       // 0: VoiceConfig
	(*AudioConfig)(nil),                       // 1: AudioConfig
	(*TtsConfig)(nil),                         // 2: TtsConfig
	(*TtsRequest)(nil),                        // 3: TtsRequest
	(*TtsRequestForConcat)(nil),               // 4: TtsRequestForConcat
	(*TtsRequestForEdit)(nil),                 // 5: TtsRequestForEdit
	(*FrontendResults)(nil),                   // 6: FrontendResults
	(*TtsResponseMeta)(nil),                   // 7: TtsResponseMeta
	(*TtsResponseChunkMeta)(nil),              // 8: TtsResponseChunkMeta
	(*TtsResponse)(nil),                       // 9: TtsResponse
	(*TtsRequestForEdit_BeginEndForEdit)(nil), // 10: TtsRequestForEdit.BeginEndForEdit
	(*FrontendResults_Word)(nil),              // 11: FrontendResults.Word
}
var file_tts_proto_depIdxs = []int32{
	0,  // 0: TtsConfig.voice:type_name -> VoiceConfig
	1,  // 1: TtsConfig.audio:type_name -> AudioConfig
	2,  // 2: TtsRequest.config:type_name -> TtsConfig
	2,  // 3: TtsRequestForConcat.config:type_name -> TtsConfig
	2,  // 4: TtsRequestForEdit.config:type_name -> TtsConfig
	10, // 5: TtsRequestForEdit.edit_positions:type_name -> TtsRequestForEdit.BeginEndForEdit
	11, // 6: FrontendResults.words:type_name -> FrontendResults.Word
	6,  // 7: TtsResponseMeta.frontend_results:type_name -> FrontendResults
	1,  // 8: TtsResponseMeta.actual_audio_config:type_name -> AudioConfig
	8,  // 9: TtsResponse.tts_response_chunk_meta:type_name -> TtsResponseChunkMeta
	7,  // 10: TtsResponse.tts_response_meta:type_name -> TtsResponseMeta
	3,  // 11: Tts.FrontEnd:input_type -> TtsRequest
	3,  // 12: Tts.Synthesis:input_type -> TtsRequest
	4,  // 13: Tts.ConcatSynthesis:input_type -> TtsRequestForConcat
	5,  // 14: Tts.EditSynthesis:input_type -> TtsRequestForEdit
	6,  // 15: Tts.FrontEnd:output_type -> FrontendResults
	9,  // 16: Tts.Synthesis:output_type -> TtsResponse
	9,  // 17: Tts.ConcatSynthesis:output_type -> TtsResponse
	9,  // 18: Tts.EditSynthesis:output_type -> TtsResponse
	15, // [15:19] is the sub-list for method output_type
	11, // [11:15] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_tts_proto_init() }
func file_tts_proto_init() {
	if File_tts_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tts_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoiceConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tts_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudioConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tts_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tts_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tts_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsRequestForConcat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tts_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsRequestForEdit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tts_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FrontendResults); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tts_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsResponseMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tts_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsResponseChunkMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tts_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tts_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsRequestForEdit_BeginEndForEdit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tts_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FrontendResults_Word); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tts_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tts_proto_goTypes,
		DependencyIndexes: file_tts_proto_depIdxs,
		MessageInfos:      file_tts_proto_msgTypes,
	}.Build()
	File_tts_proto = out.File
	file_tts_proto_rawDesc = nil
	file_tts_proto_goTypes = nil
	file_tts_proto_depIdxs = nil
}
