// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.4
// source: tts.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TtsClient is the client API for Tts service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TtsClient interface {
	// 语音合成前端接口，输入与Synthesis相同，输出可查看音素和韵律，可用于验证是否可正常合成等
	FrontEnd(ctx context.Context, in *TtsRequest, opts ...grpc.CallOption) (*FrontendResults, error)
	// 语音合成接口（无前后句）
	Synthesis(ctx context.Context, in *TtsRequest, opts ...grpc.CallOption) (Tts_SynthesisClient, error)
	// 将多条音频拼接起来
	ConcatSynthesis(ctx context.Context, in *TtsRequestForConcat, opts ...grpc.CallOption) (Tts_ConcatSynthesisClient, error)
	// 按照text内容修改音频中某个部分的读法
	EditSynthesis(ctx context.Context, in *TtsRequestForEdit, opts ...grpc.CallOption) (Tts_EditSynthesisClient, error)
}

type ttsClient struct {
	cc grpc.ClientConnInterface
}

func NewTtsClient(cc grpc.ClientConnInterface) TtsClient {
	return &ttsClient{cc}
}

func (c *ttsClient) FrontEnd(ctx context.Context, in *TtsRequest, opts ...grpc.CallOption) (*FrontendResults, error) {
	out := new(FrontendResults)
	err := c.cc.Invoke(ctx, "/Tts/FrontEnd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ttsClient) Synthesis(ctx context.Context, in *TtsRequest, opts ...grpc.CallOption) (Tts_SynthesisClient, error) {
	stream, err := c.cc.NewStream(ctx, &Tts_ServiceDesc.Streams[0], "/Tts/Synthesis", opts...)
	if err != nil {
		return nil, err
	}
	x := &ttsSynthesisClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Tts_SynthesisClient interface {
	Recv() (*TtsResponse, error)
	grpc.ClientStream
}

type ttsSynthesisClient struct {
	grpc.ClientStream
}

func (x *ttsSynthesisClient) Recv() (*TtsResponse, error) {
	m := new(TtsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *ttsClient) ConcatSynthesis(ctx context.Context, in *TtsRequestForConcat, opts ...grpc.CallOption) (Tts_ConcatSynthesisClient, error) {
	stream, err := c.cc.NewStream(ctx, &Tts_ServiceDesc.Streams[1], "/Tts/ConcatSynthesis", opts...)
	if err != nil {
		return nil, err
	}
	x := &ttsConcatSynthesisClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Tts_ConcatSynthesisClient interface {
	Recv() (*TtsResponse, error)
	grpc.ClientStream
}

type ttsConcatSynthesisClient struct {
	grpc.ClientStream
}

func (x *ttsConcatSynthesisClient) Recv() (*TtsResponse, error) {
	m := new(TtsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *ttsClient) EditSynthesis(ctx context.Context, in *TtsRequestForEdit, opts ...grpc.CallOption) (Tts_EditSynthesisClient, error) {
	stream, err := c.cc.NewStream(ctx, &Tts_ServiceDesc.Streams[2], "/Tts/EditSynthesis", opts...)
	if err != nil {
		return nil, err
	}
	x := &ttsEditSynthesisClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Tts_EditSynthesisClient interface {
	Recv() (*TtsResponse, error)
	grpc.ClientStream
}

type ttsEditSynthesisClient struct {
	grpc.ClientStream
}

func (x *ttsEditSynthesisClient) Recv() (*TtsResponse, error) {
	m := new(TtsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// TtsServer is the server API for Tts service.
// All implementations must embed UnimplementedTtsServer
// for forward compatibility
type TtsServer interface {
	// 语音合成前端接口，输入与Synthesis相同，输出可查看音素和韵律，可用于验证是否可正常合成等
	FrontEnd(context.Context, *TtsRequest) (*FrontendResults, error)
	// 语音合成接口（无前后句）
	Synthesis(*TtsRequest, Tts_SynthesisServer) error
	// 将多条音频拼接起来
	ConcatSynthesis(*TtsRequestForConcat, Tts_ConcatSynthesisServer) error
	// 按照text内容修改音频中某个部分的读法
	EditSynthesis(*TtsRequestForEdit, Tts_EditSynthesisServer) error
	mustEmbedUnimplementedTtsServer()
}

// UnimplementedTtsServer must be embedded to have forward compatible implementations.
type UnimplementedTtsServer struct {
}

func (UnimplementedTtsServer) FrontEnd(context.Context, *TtsRequest) (*FrontendResults, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FrontEnd not implemented")
}
func (UnimplementedTtsServer) Synthesis(*TtsRequest, Tts_SynthesisServer) error {
	return status.Errorf(codes.Unimplemented, "method Synthesis not implemented")
}
func (UnimplementedTtsServer) ConcatSynthesis(*TtsRequestForConcat, Tts_ConcatSynthesisServer) error {
	return status.Errorf(codes.Unimplemented, "method ConcatSynthesis not implemented")
}
func (UnimplementedTtsServer) EditSynthesis(*TtsRequestForEdit, Tts_EditSynthesisServer) error {
	return status.Errorf(codes.Unimplemented, "method EditSynthesis not implemented")
}
func (UnimplementedTtsServer) mustEmbedUnimplementedTtsServer() {}

// UnsafeTtsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TtsServer will
// result in compilation errors.
type UnsafeTtsServer interface {
	mustEmbedUnimplementedTtsServer()
}

func RegisterTtsServer(s grpc.ServiceRegistrar, srv TtsServer) {
	s.RegisterService(&Tts_ServiceDesc, srv)
}

func _Tts_FrontEnd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TtsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TtsServer).FrontEnd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Tts/FrontEnd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TtsServer).FrontEnd(ctx, req.(*TtsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tts_Synthesis_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(TtsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(TtsServer).Synthesis(m, &ttsSynthesisServer{stream})
}

type Tts_SynthesisServer interface {
	Send(*TtsResponse) error
	grpc.ServerStream
}

type ttsSynthesisServer struct {
	grpc.ServerStream
}

func (x *ttsSynthesisServer) Send(m *TtsResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Tts_ConcatSynthesis_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(TtsRequestForConcat)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(TtsServer).ConcatSynthesis(m, &ttsConcatSynthesisServer{stream})
}

type Tts_ConcatSynthesisServer interface {
	Send(*TtsResponse) error
	grpc.ServerStream
}

type ttsConcatSynthesisServer struct {
	grpc.ServerStream
}

func (x *ttsConcatSynthesisServer) Send(m *TtsResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Tts_EditSynthesis_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(TtsRequestForEdit)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(TtsServer).EditSynthesis(m, &ttsEditSynthesisServer{stream})
}

type Tts_EditSynthesisServer interface {
	Send(*TtsResponse) error
	grpc.ServerStream
}

type ttsEditSynthesisServer struct {
	grpc.ServerStream
}

func (x *ttsEditSynthesisServer) Send(m *TtsResponse) error {
	return x.ServerStream.SendMsg(m)
}

// Tts_ServiceDesc is the grpc.ServiceDesc for Tts service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Tts_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "Tts",
	HandlerType: (*TtsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FrontEnd",
			Handler:    _Tts_FrontEnd_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Synthesis",
			Handler:       _Tts_Synthesis_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "ConcatSynthesis",
			Handler:       _Tts_ConcatSynthesis_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "EditSynthesis",
			Handler:       _Tts_EditSynthesis_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "tts.proto",
}
