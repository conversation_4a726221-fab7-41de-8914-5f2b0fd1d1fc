package tts

import (
	"context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"sync"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/log"
	"tts-service/rpc/tts/pb"
)

var (
	ttsClient     *grpc.ClientConn
	ttsClientOnce sync.Once
)

func getTTSClient(ctx context.Context, address string) *grpc.ClientConn {
	ttsClientOnce.Do(func() {
		client, err := grpc.Dial(address, grpc.WithTransportCredentials(insecure.NewCredentials()))
		if err != nil {
			log.Errorf(ctx, "Failed to dial tts server: %s", err.<PERSON>rror())
			ttsClientOnce = sync.Once{}
		}

		ttsClient = client
	})
	return ttsClient
}

func GetTTSClient(ctx context.Context) pb.TtsClient {
	return pb.NewTtsClient(getTTSClient(ctx, config.Config.GetString("services.addr")))
}
