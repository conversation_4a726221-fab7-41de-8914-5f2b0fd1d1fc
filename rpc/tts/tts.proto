syntax = "proto3";

option go_package = "./pb";

message VoiceConfig {
  string language_code = 1;  // 语种名 id-ID
  string name = 2;  // 音色名, id-ID-angelina
  string ssml_gender = 3;  // 与谷歌的ssmlGender相同
}

message AudioConfig {
  string audio_encoding = 1;  // 音频返回格式，例如wav
  float speaking_rate = 2;  // 语速，1.0不变，必须要传因为默认是0
  float pitch = 3;  // 音调，0不变，越大音调越高
  float volume_gain_db = 4;  // 音量增益，为0时输出原始音量，数值越大音量越大
  int32 sample_rate_hertz = 5;  // 采样率
  string effects_profile_id = 6;  // 音效
}

message TtsConfig {
  string request_id = 1;  // 请求ID，用于定位问题
  VoiceConfig voice = 2;  // 音色的相关配置
  bool stream = 3;  // 是否流式生成
  AudioConfig audio = 4;  // 音频的相关配置
  int64 audio_chunk_us = 5;  // 期待的流式返回块的大小，stream为True时有效
}

message TtsRequest {
  TtsConfig config = 1;  // tts的配置
  string text = 2;  // 文本，有前后<speak>则认为输入是SSML
}

message TtsRequestForConcat {
  TtsConfig config = 1;  // tts的配置
  // 待合成文本，支持ssml格式，如果是ssml格式，必须以<speak>开始，以</speak>结尾
  repeated string text = 2;  // 待合成文本
  // 音频内容的参考文本，可选，如果有，和reference_audio_contents一一对应
  // 支持ssml格式，如果是ssml格式，每个文本必须以<speak>开始，以</speak>结尾
  repeated string ref_text = 3;
  // 拼接的参考音频，必须
  // wav格式的数据(不包含wav文件头), 8k, 16bit, 小端
  repeated bytes reference_audio_contents = 4;
  // 拼接的参考音频在整句中的位置, eg: [1, 0, 1]代表拼接的顺序为[音频, TTS, 音频]
  repeated bool text_and_audio_positions = 5;
}

message TtsRequestForEdit {
  TtsConfig config = 1;  // tts的配置
  repeated string text = 2;  // 文本
  // 音频内容的参考文本，可选，如果有，和reference_audio_contents一一对应
  repeated string ref_text = 3;
  bytes reference_audio_content = 4;  // 待修改的参考音频

  message BeginEndForEdit {
    // [begin_ms, end_ms)
    int64 begin_ms = 1;  // 待修改部分开始时间
    int64 end_ms = 2;  // 待修改部分结束时间
  }
  // 拼接的TTS音频在整句中的位置
  repeated BeginEndForEdit edit_positions = 5;
}

message FrontendResults {
  message Word {
    string word = 1;  // 词
    repeated string phonemes = 2;  // 词对应的音素列表
    repeated int64 phoneme_end_us = 3;  // 每个音素的时长，首尾相接
  }
  repeated string prosodies = 1;  // 每个词的韵律标签
  repeated Word words = 2;  // 词的列表
}

message TtsResponseMeta {
  int64 synthesis_begin_ms = 1;  // 当前句子的初始合成时间,UNIX_TIME(ms)
  int64 first_chunk_latency_ms = 2;  // 首块延迟
  int64 frontend_cost_ms = 3;  // 前端耗时
  FrontendResults frontend_results = 4;  // 前端的结果
  AudioConfig actual_audio_config = 5;  // 实际使用的音频配置
}

message TtsResponseChunkMeta {
  float rtf = 1;  // 当前块实时率，合成耗时除以音频的长度，
  int64 chunk_synthesis_begin_ms = 2;  // 当前块的开始合成时间, UNIX_TIME(ms)
  int32 chunk_id = 3;  // 当前是流式的第几块，从0开始
  int64 chunk_size_us = 4;  // 当前块的大小(us)
}

message TtsResponse {
  bytes audio_content = 1;  // 合成的音频
  string status = 2;  // 状态，失败时给出失败信息，成功后返回OK
  bool is_end = 3;  // 是否是流式的最后一块，非流式情况也设置为True
  int32 code = 4;  // 返回的code，code为0时，有音频，否则失败
  TtsResponseChunkMeta tts_response_chunk_meta = 5;  // 返回的块的状态特征
  TtsResponseMeta tts_response_meta = 6;  // 返回的每句的状态特征，伴随第一块音频返回
}

service Tts {
  // 语音合成前端接口，输入与Synthesis相同，输出可查看音素和韵律，可用于验证是否可正常合成等
  rpc FrontEnd(TtsRequest) returns (FrontendResults) {}
  // 语音合成接口（无前后句）
  rpc Synthesis (TtsRequest) returns (stream TtsResponse) {}
  // 将多条音频拼接起来
  rpc ConcatSynthesis (TtsRequestForConcat) returns (stream TtsResponse) {}
  // 按照text内容修改音频中某个部分的读法
  rpc EditSynthesis(TtsRequestForEdit) returns (stream TtsResponse) {}
}
