// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.19.4
// source: stream.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MicrosoftTTSClient is the client API for MicrosoftTTS service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MicrosoftTTSClient interface {
	// 语音合成接口（无前后句）
	Synthesis(ctx context.Context, in *MicrosoftTtsRequest, opts ...grpc.CallOption) (MicrosoftTTS_SynthesisClient, error)
}

type microsoftTTSClient struct {
	cc grpc.ClientConnInterface
}

func NewMicrosoftTTSClient(cc grpc.ClientConnInterface) MicrosoftTTSClient {
	return &microsoftTTSClient{cc}
}

func (c *microsoftTTSClient) Synthesis(ctx context.Context, in *MicrosoftTtsRequest, opts ...grpc.CallOption) (MicrosoftTTS_SynthesisClient, error) {
	stream, err := c.cc.NewStream(ctx, &MicrosoftTTS_ServiceDesc.Streams[0], "/MicrosoftTTS/Synthesis", opts...)
	if err != nil {
		return nil, err
	}
	x := &microsoftTTSSynthesisClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type MicrosoftTTS_SynthesisClient interface {
	Recv() (*MicrosoftTtsResponse, error)
	grpc.ClientStream
}

type microsoftTTSSynthesisClient struct {
	grpc.ClientStream
}

func (x *microsoftTTSSynthesisClient) Recv() (*MicrosoftTtsResponse, error) {
	m := new(MicrosoftTtsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// MicrosoftTTSServer is the server API for MicrosoftTTS service.
// All implementations must embed UnimplementedMicrosoftTTSServer
// for forward compatibility
type MicrosoftTTSServer interface {
	// 语音合成接口（无前后句）
	Synthesis(*MicrosoftTtsRequest, MicrosoftTTS_SynthesisServer) error
	mustEmbedUnimplementedMicrosoftTTSServer()
}

// UnimplementedMicrosoftTTSServer must be embedded to have forward compatible implementations.
type UnimplementedMicrosoftTTSServer struct {
}

func (UnimplementedMicrosoftTTSServer) Synthesis(*MicrosoftTtsRequest, MicrosoftTTS_SynthesisServer) error {
	return status.Errorf(codes.Unimplemented, "method Synthesis not implemented")
}
func (UnimplementedMicrosoftTTSServer) mustEmbedUnimplementedMicrosoftTTSServer() {}

// UnsafeMicrosoftTTSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MicrosoftTTSServer will
// result in compilation errors.
type UnsafeMicrosoftTTSServer interface {
	mustEmbedUnimplementedMicrosoftTTSServer()
}

func RegisterMicrosoftTTSServer(s grpc.ServiceRegistrar, srv MicrosoftTTSServer) {
	s.RegisterService(&MicrosoftTTS_ServiceDesc, srv)
}

func _MicrosoftTTS_Synthesis_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(MicrosoftTtsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(MicrosoftTTSServer).Synthesis(m, &microsoftTTSSynthesisServer{stream})
}

type MicrosoftTTS_SynthesisServer interface {
	Send(*MicrosoftTtsResponse) error
	grpc.ServerStream
}

type microsoftTTSSynthesisServer struct {
	grpc.ServerStream
}

func (x *microsoftTTSSynthesisServer) Send(m *MicrosoftTtsResponse) error {
	return x.ServerStream.SendMsg(m)
}

// MicrosoftTTS_ServiceDesc is the grpc.ServiceDesc for MicrosoftTTS service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MicrosoftTTS_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "MicrosoftTTS",
	HandlerType: (*MicrosoftTTSServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Synthesis",
			Handler:       _MicrosoftTTS_Synthesis_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "stream.proto",
}
