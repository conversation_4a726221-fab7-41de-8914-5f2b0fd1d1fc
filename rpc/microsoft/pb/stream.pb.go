// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.19.4
// source: stream.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MicrosoftTtsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId        string  `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Locale           string  `protobuf:"bytes,2,opt,name=locale,proto3" json:"locale,omitempty"`
	Speaker          string  `protobuf:"bytes,3,opt,name=speaker,proto3" json:"speaker,omitempty"`
	SpeakingRate     float64 `protobuf:"fixed64,4,opt,name=speaking_rate,json=speakingRate,proto3" json:"speaking_rate,omitempty"`
	Volume           float64 `protobuf:"fixed64,5,opt,name=volume,proto3" json:"volume,omitempty"`
	Ssml             string  `protobuf:"bytes,6,opt,name=ssml,proto3" json:"ssml,omitempty"`
	Provider         string  `protobuf:"bytes,7,opt,name=provider,proto3" json:"provider,omitempty"`
	StreamingLatency int64   `protobuf:"varint,8,opt,name=streaming_latency,json=streamingLatency,proto3" json:"streaming_latency,omitempty"`
	Stability        int64   `protobuf:"varint,9,opt,name=stability,proto3" json:"stability,omitempty"`
	SimilarityBoost  int64   `protobuf:"varint,10,opt,name=similarity_boost,json=similarityBoost,proto3" json:"similarity_boost,omitempty"`
	ElevenlabsKey    string  `protobuf:"bytes,11,opt,name=elevenlabs_key,json=elevenlabsKey,proto3" json:"elevenlabs_key,omitempty"`
}

func (x *MicrosoftTtsRequest) Reset() {
	*x = MicrosoftTtsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_stream_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MicrosoftTtsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MicrosoftTtsRequest) ProtoMessage() {}

func (x *MicrosoftTtsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_stream_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MicrosoftTtsRequest.ProtoReflect.Descriptor instead.
func (*MicrosoftTtsRequest) Descriptor() ([]byte, []int) {
	return file_stream_proto_rawDescGZIP(), []int{0}
}

func (x *MicrosoftTtsRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *MicrosoftTtsRequest) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

func (x *MicrosoftTtsRequest) GetSpeaker() string {
	if x != nil {
		return x.Speaker
	}
	return ""
}

func (x *MicrosoftTtsRequest) GetSpeakingRate() float64 {
	if x != nil {
		return x.SpeakingRate
	}
	return 0
}

func (x *MicrosoftTtsRequest) GetVolume() float64 {
	if x != nil {
		return x.Volume
	}
	return 0
}

func (x *MicrosoftTtsRequest) GetSsml() string {
	if x != nil {
		return x.Ssml
	}
	return ""
}

func (x *MicrosoftTtsRequest) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *MicrosoftTtsRequest) GetStreamingLatency() int64 {
	if x != nil {
		return x.StreamingLatency
	}
	return 0
}

func (x *MicrosoftTtsRequest) GetStability() int64 {
	if x != nil {
		return x.Stability
	}
	return 0
}

func (x *MicrosoftTtsRequest) GetSimilarityBoost() int64 {
	if x != nil {
		return x.SimilarityBoost
	}
	return 0
}

func (x *MicrosoftTtsRequest) GetElevenlabsKey() string {
	if x != nil {
		return x.ElevenlabsKey
	}
	return ""
}

type MicrosoftTtsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AudioContent []byte  `protobuf:"bytes,1,opt,name=audio_content,json=audioContent,proto3" json:"audio_content,omitempty"`
	Status       string  `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	IsEnd        bool    `protobuf:"varint,3,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	Code         int64   `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Duration     int64   `protobuf:"varint,5,opt,name=duration,proto3" json:"duration,omitempty"`
	Rtf          float64 `protobuf:"fixed64,6,opt,name=rtf,proto3" json:"rtf,omitempty"`
}

func (x *MicrosoftTtsResponse) Reset() {
	*x = MicrosoftTtsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_stream_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MicrosoftTtsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MicrosoftTtsResponse) ProtoMessage() {}

func (x *MicrosoftTtsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_stream_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MicrosoftTtsResponse.ProtoReflect.Descriptor instead.
func (*MicrosoftTtsResponse) Descriptor() ([]byte, []int) {
	return file_stream_proto_rawDescGZIP(), []int{1}
}

func (x *MicrosoftTtsResponse) GetAudioContent() []byte {
	if x != nil {
		return x.AudioContent
	}
	return nil
}

func (x *MicrosoftTtsResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *MicrosoftTtsResponse) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

func (x *MicrosoftTtsResponse) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *MicrosoftTtsResponse) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *MicrosoftTtsResponse) GetRtf() float64 {
	if x != nil {
		return x.Rtf
	}
	return 0
}

var File_stream_proto protoreflect.FileDescriptor

var file_stream_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf0,
	0x02, 0x0a, 0x13, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x6f, 0x66, 0x74, 0x54, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x61, 0x6b,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c,
	0x73, 0x70, 0x65, 0x61, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x73, 0x6d, 0x6c, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x73, 0x73, 0x6d, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x69, 0x6e,
	0x67, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x10, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x4c, 0x61, 0x74, 0x65, 0x6e, 0x63,
	0x79, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12,
	0x29, 0x0a, 0x10, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x6f,
	0x6f, 0x73, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x69, 0x6d, 0x69, 0x6c,
	0x61, 0x72, 0x69, 0x74, 0x79, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6c,
	0x65, 0x76, 0x65, 0x6e, 0x6c, 0x61, 0x62, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x65, 0x6c, 0x65, 0x76, 0x65, 0x6e, 0x6c, 0x61, 0x62, 0x73, 0x4b, 0x65,
	0x79, 0x22, 0xac, 0x01, 0x0a, 0x14, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x6f, 0x66, 0x74, 0x54,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x75,
	0x64, 0x69, 0x6f, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0c, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x65, 0x6e,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x45, 0x6e, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x72, 0x74, 0x66, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x72, 0x74, 0x66,
	0x32, 0x4c, 0x0a, 0x0c, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x6f, 0x66, 0x74, 0x54, 0x54, 0x53,
	0x12, 0x3c, 0x0a, 0x09, 0x53, 0x79, 0x6e, 0x74, 0x68, 0x65, 0x73, 0x69, 0x73, 0x12, 0x14, 0x2e,
	0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x6f, 0x66, 0x74, 0x54, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x6f, 0x66, 0x74, 0x54,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x30, 0x01, 0x42, 0x06,
	0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_stream_proto_rawDescOnce sync.Once
	file_stream_proto_rawDescData = file_stream_proto_rawDesc
)

func file_stream_proto_rawDescGZIP() []byte {
	file_stream_proto_rawDescOnce.Do(func() {
		file_stream_proto_rawDescData = protoimpl.X.CompressGZIP(file_stream_proto_rawDescData)
	})
	return file_stream_proto_rawDescData
}

var file_stream_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_stream_proto_goTypes = []interface{}{
	(*MicrosoftTtsRequest)(nil),  // 0: MicrosoftTtsRequest
	(*MicrosoftTtsResponse)(nil), // 1: MicrosoftTtsResponse
}
var file_stream_proto_depIdxs = []int32{
	0, // 0: MicrosoftTTS.Synthesis:input_type -> MicrosoftTtsRequest
	1, // 1: MicrosoftTTS.Synthesis:output_type -> MicrosoftTtsResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_stream_proto_init() }
func file_stream_proto_init() {
	if File_stream_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_stream_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MicrosoftTtsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_stream_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MicrosoftTtsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_stream_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_stream_proto_goTypes,
		DependencyIndexes: file_stream_proto_depIdxs,
		MessageInfos:      file_stream_proto_msgTypes,
	}.Build()
	File_stream_proto = out.File
	file_stream_proto_rawDesc = nil
	file_stream_proto_goTypes = nil
	file_stream_proto_depIdxs = nil
}
