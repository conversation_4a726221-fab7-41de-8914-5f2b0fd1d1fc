package microsoft

import (
	"context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"sync"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/log"
	"tts-service/rpc/microsoft/pb"
)

var (
	client     *grpc.ClientConn
	clientOnce sync.Once
)

func getMicrosoftClient(ctx context.Context, address string) *grpc.ClientConn {
	clientOnce.Do(func() {
		var err error
		client, err = grpc.Dial(address, grpc.WithTransportCredentials(insecure.NewCredentials()))
		if err != nil {
			log.Errorf(ctx, "Failed to dial microsoft server: %s", err.Error())
			clientOnce = sync.Once{}
		}
	})
	return client
}

func GetStreamTTSClient(ctx context.Context) pb.MicrosoftTTSClient {
	return pb.NewMicrosoftTTSClient(getMicrosoftClient(ctx, config.Config.GetString("services.microsoft_addr")))
}
