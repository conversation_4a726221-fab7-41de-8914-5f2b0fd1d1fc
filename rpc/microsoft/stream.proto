syntax = "proto3";

option go_package = "./pb";

service MicrosoftTTS {
  // 语音合成接口（无前后句）
  rpc Synthesis (MicrosoftTtsRequest) returns (stream MicrosoftTtsResponse) {}
}

message MicrosoftTtsRequest {
  string request_id = 1;
  string locale = 2;
  string speaker = 3;
  double speaking_rate = 4;
  double volume = 5;
  string ssml = 6;
  string provider = 7;

  int64 streaming_latency = 8;
  int64 stability = 9;
  int64 similarity_boost = 10;
  string elevenlabs_key = 11;
}

message MicrosoftTtsResponse {
  bytes audio_content = 1;
  string status = 2;
  bool is_end = 3;
  int64 code = 4;
  int64 duration = 5;
  double rtf = 6;
}
