FROM golang:1.19.2-alpine3.16 AS Builder
ADD . /tts-service

RUN apk add --no-cache git gcc
RUN cd /tts-service && GOOS=linux GOARCH=amd64 go build -o tts-service cmd/tts-service/tts.go

FROM jrottenberg/ffmpeg:4.0-alpine

RUN apk update && apk add tzdata
VOLUME /tts-service/logs
VOLUME /tts-service/audio

COPY --from=Builder /tts-service/html /tts-service/html
COPY --from=Builder /tts-service/tts-service /tts-service/tts-service
COPY --from=Builder /tts-service/accounts /tts-service/accounts

EXPOSE 10088
EXPOSE 10088
WORKDIR /tts-service
ENTRYPOINT ["/tts-service/tts-service"]
