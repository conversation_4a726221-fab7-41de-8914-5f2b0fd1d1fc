from loguru import logger

import grpc
from server.tts import StreamTTS
from rpc.stream_pb2_grpc import add_MicrosoftTTSServicer_to_server


async def serve(addr: str):
    server = grpc.aio.server()
    add_MicrosoftTTSServicer_to_server(StreamTTS(), server)
    server.add_insecure_port(addr)
    logger.info(f"Starting server on {addr}")

    await server.start()
    await server.wait_for_termination()
