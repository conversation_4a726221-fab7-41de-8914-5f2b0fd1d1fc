import os
import av
import requests
import io

from rpc.stream_pb2 import MicrosoftTtsRequest, MicrosoftTtsResponse

# 16bit 为 1毫秒的音频数据
buffer_ms = 200
basic_size = 16
buffer_size = 16 * buffer_ms
basic_duration = buffer_ms * 1000
first_frame_size = 74

CHUNK_SIZE = 1024

region_file = os.environ.get("elevenlabs_key")


def to_file_like_obj(iterable):
    chunk = b''
    offset = 0
    it = iter(iterable)

    def up_to_iter(size):
        nonlocal chunk, offset

        while size:
            if offset == len(chunk):
                try:
                    chunk = next(it)
                except StopIteration:
                    break
                else:
                    offset = 0
            to_yield = min(size, len(chunk) - offset)
            offset = offset + to_yield
            size -= to_yield

            yield chunk[offset - to_yield:offset]

    class FileLikeObj:
        def read(self, size=-1):
            return b''.join(up_to_iter(float('inf') if size is None or size < 0 else size))

    return FileLikeObj()


def generate_elevenlabs_Audio(request: MicrosoftTtsRequest) -> MicrosoftTtsResponse:
    url = f"https://api.elevenlabs.io/v1/text-to-speech/{request.speaker}/stream?optimize_streaming_latency={request.streaming_latency}"
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": request.elevenlabs_key,
    }

    stability, similarity_boost = 0, 0
    if request.stability > 0:
        stability = request.stability * 1.0 / 100

    if request.similarity_boost > 0:
        similarity_boost = request.similarity_boost * 1.0 / 100

    body = {
        "text": request.ssml,
        "model_id": request.locale,
        "voice_settings": {
            "stability": stability,
            "similarity_boost": similarity_boost,
        }
    }

    response = requests.post(url, json=body, headers=headers, stream=True)

    if response.status_code != 200:
        yield MicrosoftTtsResponse(
            code=response.status_code,
            status=str(response.content),
            is_end=True
        )
        return

    input = av.open(to_file_like_obj(response.iter_content(CHUNK_SIZE)))
    stream = input.streams.get(audio=0)[0]
    bio = io.BytesIO()

    audio_buffer = bytes()

    is_first = True
    with av.open(bio, 'w', 'wav') as out_container:
        out_stream = out_container.add_stream(
            'pcm_s16le',
            rate=8000,
            layout='mono'
        )

        for frame in input.decode(stream):
            for packet in out_stream.encode(frame):
                current = bio.tell()
                out_container.mux(packet)
                bio.seek(current)
                audio_buffer += bio.read()

                if is_first and len(audio_buffer) >= first_frame_size:
                    is_first = False
                    audio_buffer = audio_buffer[first_frame_size:]

                if len(audio_buffer) >= buffer_size:
                    yield MicrosoftTtsResponse(
                        audio_content=audio_buffer[:buffer_size],
                        code=200,
                        duration=basic_duration
                    )
                    audio_buffer = audio_buffer[buffer_size:]

    yield MicrosoftTtsResponse(
        audio_content=audio_buffer,
        code=200,
        duration=(len(audio_buffer) // basic_size) * 1000,
        is_end=True,
    )
