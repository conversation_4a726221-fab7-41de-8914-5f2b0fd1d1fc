import traceback

import grpc.aio
from loguru import logger

from rpc.stream_pb2_grpc import MicrosoftTTSServicer
from rpc.stream_pb2 import MicrosoftTtsRequest, MicrosoftTtsResponse
from server.microsoft import generate_microsoft_Audio
from server.elevenlabs import generate_elevenlabs_Audio

# 16bit 为 1毫秒的音频数据
basic_size = 16
buffer_size = 16 * 200


class StreamTTS(MicrosoftTTSServicer):
    async def Synthesis(self, request: MicrosoftTtsRequest, context: grpc.aio.ServicerContext) -> MicrosoftTtsResponse:
        logger.info(f"接收到调用请求: provider={request.provider}, call_id={request.request_id}, locale={request.locale}, speaker={request.speaker}, ssml={request.ssml}")
        if request.provider == "elevenlabs":
            generate_audio = generate_elevenlabs_Audio(request)
        else:
            generate_audio = generate_microsoft_Audio(request)

        for response in generate_audio:
            yield response
