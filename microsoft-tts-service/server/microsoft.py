import traceback

from loguru import logger
from client.client import get_common_client
from azure.cognitiveservices import speech

from rpc.stream_pb2 import MicrosoftTtsRequest, MicrosoftTtsResponse
from tts.silence import silence_pcm

# 16bit 为 1毫秒的音频数据
basic_size = 16
buffer_size = 16 * 200


def generate_microsoft_Audio(request: MicrosoftTtsRequest) -> MicrosoftTtsResponse:
    client, speech_synthesizer = get_common_client(request.locale, request.speaker)

    # result = speech_synthesizer.start_speaking_text_async(request.ssml).get()
    result = speech_synthesizer.start_speaking_ssml_async(request.ssml).get()
    audio_data_stream = speech.AudioDataStream(result)

    audio_buffer = bytes(buffer_size)

    index = 1
    while True:
        filled_size = audio_data_stream.read_data(audio_buffer)
        if audio_data_stream.cancellation_details:
            error_detail = audio_data_stream.cancellation_details
            logger.error(
                f"请求发生错误：call_id=%s, error_details={error_detail.error_details}, error_code={error_detail.error_code}")

            yield MicrosoftTtsResponse(
                code=error_detail.error_code.value if error_detail.error_code else 500001,
                status=error_detail.error_details,
                is_end=True)

            return

        if filled_size == 0:
            break

        logger.info(
            f"数据包生成：index={index}, duration={(filled_size // basic_size) * 1000}, filled_size={filled_size}")

        audio_content = audio_buffer[:filled_size]
        if index == 1:
            try:
                audio_content = silence_pcm(audio_content)
            except Exception:
                traceback.print_exc()

        yield MicrosoftTtsResponse(
            audio_content=audio_content,
            code=200,
            duration=(len(audio_content) // basic_size) * 1000
        )
        index += 1

    yield MicrosoftTtsResponse(
        code=200,
        is_end=True
    )
