FROM airudder-registry.cn-hongkong.cr.aliyuncs.com/airudder/microsoft-tts-service:1684224638

ENV PYTHONUNBUFFERED 1

COPY . /microsoft-tts-service
WORKDIR /microsoft-tts-service

RUN pip3 install -r requirements.txt
CMD ["/microsoft-tts-service/main.py"]
ENTRYPOINT ["python"]



#FROM python:3.10.10-slim
#ENV PYTHONUNBUFFERED 1
#
#COPY . /microsoft-tts-service
#WORKDIR /microsoft-tts-service
#
#RUN apt-get update && apt-get install -y libssl-dev libasound2 ffmpeg
#RUN pip3 install -r requirements.txt
#
#CMD ["/microsoft-tts-service/main.py"]
#ENTRYPOINT ["python"]
