import csv
import itertools
import os
import threading
import typing
from azure.cognitiveservices import speech

locker = threading.Lock()
client_map = {}

base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
region_file = os.environ.get("region_file") or 'aliyun-hk.csv'


class Client:
    """
    微软客户端信息，从配置的 csv 文件中获取到的账号信息
    """

    def __init__(self, region: str = '', endpoint: str = '', resource: str = '', key: str = ''):
        """
        客户端初始化方法
        @param region: region
        @param endpoint: endpoint
        @param resource: 资源
        @param key: 秘钥
        """
        self.region = region
        self.endpoint = endpoint
        self.resource = resource
        self.key = key

    def get_info(self) -> typing.Dict[str, str]:
        """
        返回客户端信息的简单描述
        @return: 包含信息字段的字典
        """
        return {
            'region': self.region,
            'endpoint': self.endpoint,
            'resource': self.resource
        }


def _get_client(filename: str) -> typing.Iterator[Client]:
    """
    根据目录文件夹，返回同一类型的账号的迭代器
    @param filepath: 目录文件夹
    @return: Iterator[client] 迭代器
    """
    clients = []

    # 根据配置文件生成账号存储目录、地址
    def generate_file(*paths: str) -> str:
        return os.path.join(base_dir, 'account', *paths)

    with open(generate_file(filename)) as csv_file:
        csv_reader = csv.DictReader(csv_file, delimiter=',')
        # 根据 csv 信息生成账号信息
        for row in csv_reader:
            clients.append(Client(**row))

    # 返回一个无限循环的迭代器
    return itertools.cycle(clients)


clients = _get_client(region_file)


def get_common_client(locale: str, speaker: str) -> (Client, speech.SpeechSynthesizer):
    """
    无限循环，按照顺序返回微软账号数据
    @return: client
    """
    client = next(clients)

    unique_key = f'{client.key}-{locale}-{speaker}'
    if unique_key in client_map:
        return client, client_map[unique_key]

    with locker:
        # 二次检查，否则重复生成
        if unique_key in client_map:
            return client, client_map[unique_key]

        speech_config = speech.SpeechConfig(subscription=client.key, region=client.region)
        speech_config.set_speech_synthesis_output_format(speech.SpeechSynthesisOutputFormat.Raw8Khz16BitMonoPcm)
        # 设置区域
        speech_config.speech_synthesis_language = locale
        # 设置音色
        speech_config.speech_synthesis_voice_name = speaker
        audio_config = speech.audio.AudioOutputConfig(use_default_speaker=True)

        # 这里使用 audio_config=None，此时 sdk 不回去额外播放音频内容，也不会保存到本地文件中
        client_map[unique_key] = speech.SpeechSynthesizer(speech_config=speech_config, audio_config=audio_config)
        return client, client_map[unique_key]
