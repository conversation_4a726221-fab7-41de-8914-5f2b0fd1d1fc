import io
import traceback

from pydub import AudioSegment, silence


def silence_pcm(pcm_audio):
    pcm_file = io.BytesIO(pcm_audio)
    audio = AudioSegment.from_file(pcm_file, format="raw", frame_rate=8000, channels=1, sample_width=2)

    audio_chunks = silence.split_on_silence(audio, min_silence_len=50, silence_thresh=-32, keep_silence=15)
    res = audio[:1]
    for i, chunk in enumerate(audio_chunks):
        res = res + chunk

    return res.raw_data
