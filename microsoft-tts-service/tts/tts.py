import datetime
import time

from client.client import get_common_client
from azure.cognitiveservices import speech


def test_tts():
    client, speech_synthesizer = get_common_client("en-US", "en-US-AmberNeural2")

    text = '''Our comprehensive car insurance policy covers loss or damage to your vehicle due to accidents, theft, 
    fire, or natural disasters. It also includes third-party liability coverage, personal accident coverage, 
    and 24-hour roadside assistance. If you are interested in purchasing our insurance policy, I can provide you with 
    a quotation and assist you with the payment process. Additionally, if you participate in our ongoing promotion, 
    you will receive a RM20 cash rebate'''

    text = "I am OK"
    # result = speech_synthesizer.speak_text_async(text).get()

    result = speech_synthesizer.start_speaking_text_async(text).get()
    audio_data_stream = speech.AudioDataStream(result)
    audio_buffer = bytes(16000)
    filled_size = audio_data_stream.read_data(audio_buffer)

    file_content = []
    file_content.extend(audio_buffer)

    while filled_size > 0:
        print("{} bytes received.".format(filled_size))
        filled_size = audio_data_stream.read_data(audio_buffer)
        print(audio_buffer[:10])
    #
        file_content.extend(audio_buffer[:filled_size])

    print(len(file_content))

    with open("/Users/<USER>/Desktop/a.wav", 'wb') as out:
        out.write(bytes(file_content))

    # first_byte_latency = int(
    #     result.properties.get_property(speech.PropertyId.SpeechServiceResponse_SynthesisFirstByteLatencyMs))
    # finished_latency = int(
    #     result.properties.get_property(speech.PropertyId.SpeechServiceResponse_SynthesisFinishLatencyMs))

    # print(f"OK-{first_byte_latency}-{finished_latency}")

    # if result.reason == speech.ResultReason.SynthesizingAudioCompleted:
    # else:
    #     # 获取错误原因，写入日志中
    #     error_detail = result.cancellation_details
    #     error_str = f'Reason={error_detail.reason}, detail={error_detail.error_details}'
    #     print(error_str)
