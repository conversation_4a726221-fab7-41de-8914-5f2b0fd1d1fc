# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: stream.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0cstream.proto\"\xf1\x01\n\x13MicrosoftTtsRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x0e\n\x06locale\x18\x02 \x01(\t\x12\x0f\n\x07speaker\x18\x03 \x01(\t\x12\x15\n\rspeaking_rate\x18\x04 \x01(\x01\x12\x0e\n\x06volume\x18\x05 \x01(\x01\x12\x0c\n\x04ssml\x18\x06 \x01(\t\x12\x10\n\x08provider\x18\x07 \x01(\t\x12\x19\n\x11streaming_latency\x18\x08 \x01(\x03\x12\x11\n\tstability\x18\t \x01(\x03\x12\x18\n\x10similarity_boost\x18\n \x01(\x03\x12\x16\n\x0e\x65levenlabs_key\x18\x0b \x01(\t\"z\n\x14MicrosoftTtsResponse\x12\x15\n\raudio_content\x18\x01 \x01(\x0c\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0e\n\x06is_end\x18\x03 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x04 \x01(\x03\x12\x10\n\x08\x64uration\x18\x05 \x01(\x03\x12\x0b\n\x03rtf\x18\x06 \x01(\x01\x32L\n\x0cMicrosoftTTS\x12<\n\tSynthesis\x12\x14.MicrosoftTtsRequest\x1a\x15.MicrosoftTtsResponse\"\x00\x30\x01\x42\x06Z\x04./pbb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'stream_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\004./pb'
  _MICROSOFTTTSREQUEST._serialized_start=17
  _MICROSOFTTTSREQUEST._serialized_end=258
  _MICROSOFTTTSRESPONSE._serialized_start=260
  _MICROSOFTTTSRESPONSE._serialized_end=382
  _MICROSOFTTTS._serialized_start=384
  _MICROSOFTTTS._serialized_end=460
# @@protoc_insertion_point(module_scope)
