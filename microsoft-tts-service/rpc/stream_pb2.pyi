from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class MicrosoftTtsRequest(_message.Message):
    __slots__ = ["elevenlabs_key", "locale", "provider", "request_id", "similarity_boost", "speaker", "speaking_rate", "ssml", "stability", "streaming_latency", "volume"]
    ELEVENLABS_KEY_FIELD_NUMBER: _ClassVar[int]
    LOCALE_FIELD_NUMBER: _ClassVar[int]
    PROVIDER_FIELD_NUMBER: _ClassVar[int]
    REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    SIMILARITY_BOOST_FIELD_NUMBER: _ClassVar[int]
    SPEAKER_FIELD_NUMBER: _ClassVar[int]
    SPEAKING_RATE_FIELD_NUMBER: _ClassVar[int]
    SSML_FIELD_NUMBER: _ClassVar[int]
    STABILITY_FIELD_NUMBER: _ClassVar[int]
    STREAMING_LATENCY_FIELD_NUMBER: _ClassVar[int]
    VOLUME_FIELD_NUMBER: _ClassVar[int]
    elevenlabs_key: str
    locale: str
    provider: str
    request_id: str
    similarity_boost: int
    speaker: str
    speaking_rate: float
    ssml: str
    stability: int
    streaming_latency: int
    volume: float
    def __init__(self, request_id: _Optional[str] = ..., locale: _Optional[str] = ..., speaker: _Optional[str] = ..., speaking_rate: _Optional[float] = ..., volume: _Optional[float] = ..., ssml: _Optional[str] = ..., provider: _Optional[str] = ..., streaming_latency: _Optional[int] = ..., stability: _Optional[int] = ..., similarity_boost: _Optional[int] = ..., elevenlabs_key: _Optional[str] = ...) -> None: ...

class MicrosoftTtsResponse(_message.Message):
    __slots__ = ["audio_content", "code", "duration", "is_end", "rtf", "status"]
    AUDIO_CONTENT_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    IS_END_FIELD_NUMBER: _ClassVar[int]
    RTF_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    audio_content: bytes
    code: int
    duration: int
    is_end: bool
    rtf: float
    status: str
    def __init__(self, audio_content: _Optional[bytes] = ..., status: _Optional[str] = ..., is_end: bool = ..., code: _Optional[int] = ..., duration: _Optional[int] = ..., rtf: _Optional[float] = ...) -> None: ...
