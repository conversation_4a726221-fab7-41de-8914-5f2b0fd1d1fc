# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import rpc.stream_pb2 as stream__pb2


class MicrosoftTTSStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Synthesis = channel.unary_stream(
                '/MicrosoftTTS/Synthesis',
                request_serializer=stream__pb2.MicrosoftTtsRequest.SerializeToString,
                response_deserializer=stream__pb2.MicrosoftTtsResponse.FromString,
                )


class MicrosoftTTSServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Synthesis(self, request, context):
        """语音合成接口（无前后句）
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_MicrosoftTTSServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Synthesis': grpc.unary_stream_rpc_method_handler(
                    servicer.Synthesis,
                    request_deserializer=stream__pb2.MicrosoftTtsRequest.FromString,
                    response_serializer=stream__pb2.MicrosoftTtsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'MicrosoftTTS', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class MicrosoftTTS(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Synthesis(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/MicrosoftTTS/Synthesis',
            stream__pb2.MicrosoftTtsRequest.SerializeToString,
            stream__pb2.MicrosoftTtsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
