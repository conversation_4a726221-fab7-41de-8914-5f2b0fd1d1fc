package main

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/joho/godotenv"
)

// 配置结构体
type Config struct {
	ApiKey   string
	VoiceID  string
	ModelID  string
	Endpoint string
}

// WebSocket消息结构体
type WSMessage struct {
	Text          string         `json:"text,omitempty"`
	ContextID     string         `json:"context_id,omitempty"`
	VoiceSettings *VoiceSettings `json:"voice_settings,omitempty"`
	Flush         bool           `json:"flush,omitempty"`
	CloseContext  bool           `json:"close_context,omitempty"`
	CloseSocket   bool           `json:"close_socket,omitempty"`
}

type VoiceSettings struct {
	Stability       float64 `json:"stability"`
	SimilarityBoost float64 `json:"similarity_boost"`
}

// API响应结构体
type APIResponse struct {
	ContextID string `json:"contextId"`
	Audio     string `json:"audio,omitempty"`
	IsFinal   bool   `json:"is_final,omitempty"`
}

func main() {
	// 加载环境变量
	err := godotenv.Load()
	if err != nil {
		log.Fatal("Error loading .env file")
	}

	// 配置初始化
	cfg := Config{
		ApiKey:   os.Getenv("ELEVENLABS_API_KEY"),
		VoiceID:  "your_voice_id",
		ModelID:  "eleven_flash_v2_5",
		Endpoint: fmt.Sprintf("wss://api.elevenlabs.io/v1/text-to-speech/%s/multi-stream-input?model_id=eleven_flash_v2_5", "your_voice_id"),
	}

	// 运行演示
	if err := conversationAgentDemo(cfg); err != nil {
		log.Fatalf("Demo failed: %v", err)
	}
}

// 主演示函数
func conversationAgentDemo(cfg Config) error {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建WebSocket连接
	conn, _, err := websocket.DefaultDialer.Dial(cfg.Endpoint, map[string][]string{
		"xi-api-key": {cfg.ApiKey},
	})
	if err != nil {
		return fmt.Errorf("dial failed: %w", err)
	}
	defer conn.Close()

	// 启动消息接收器
	var wg sync.WaitGroup
	wg.Add(1)
	go receiveMessages(ctx, conn, &wg)

	// 初始问候
	if err := sendTextInContext(conn, "Hello! I'm your virtual assistant. I can help you with a wide range of topics. What would you like to know about today?", "greeting", &VoiceSettings{
		Stability:       0.5,
		SimilarityBoost: 0.8,
	}); err != nil {
		return err
	}

	// 模拟用户等待
	time.Sleep(2 * time.Second)

	// 模拟用户打断
	fmt.Println("USER INTERRUPTS: 'Can you tell me about the weather?'")
	if err := handleInterruption(conn, "greeting", "weather_response", "I'd be happy to tell you about the weather. Currently in your area, it's 72 degrees and sunny with a slight chance of rain later this afternoon."); err != nil {
		return err
	}

	// 继续天气上下文
	if err := continueContext(conn, " If you're planning to go outside, you might want to bring a light jacket just in case.", "weather_response"); err != nil {
		return err
	}

	// 强制刷新当前上下文
	if err := flushContext(conn, "weather_response"); err != nil {
		return err
	}

	// 模拟用户等待
	time.Sleep(3 * time.Second)

	// 用户新问题
	fmt.Println("USER: 'What about tomorrow?'")
	if err := sendTextInContext(conn, "Tomorrow's forecast shows temperatures around 75 degrees with partly cloudy skies. It should be a beautiful day overall!", "tomorrow_weather", nil); err != nil {
		return err
	}

	// 刷新并关闭上下文
	if err := flushContext(conn, "tomorrow_weather"); err != nil {
		return err
	}
	if err := closeContext(conn, "tomorrow_weather"); err != nil {
		return err
	}

	// 结束对话
	time.Sleep(2 * time.Second)
	if err := endConversation(conn); err != nil {
		return err
	}

	// 等待接收协程结束
	cancel()
	wg.Wait()

	return nil
}

// 核心操作函数 ===================================================
func sendTextInContext(conn *websocket.Conn, text, contextID string, voiceSettings *VoiceSettings) error {
	msg := WSMessage{
		Text:          text,
		ContextID:     contextID,
		VoiceSettings: voiceSettings,
	}
	return sendJSON(conn, msg)
}

func continueContext(conn *websocket.Conn, text, contextID string) error {
	msg := WSMessage{
		Text:      text,
		ContextID: contextID,
	}
	return sendJSON(conn, msg)
}

func flushContext(conn *websocket.Conn, contextID string) error {
	msg := WSMessage{
		ContextID: contextID,
		Flush:     true,
	}
	return sendJSON(conn, msg)
}

func closeContext(conn *websocket.Conn, contextID string) error {
	msg := WSMessage{
		ContextID:    contextID,
		CloseContext: true,
	}
	return sendJSON(conn, msg)
}

func handleInterruption(conn *websocket.Conn, oldContextID, newContextID, newResponse string) error {
	// 关闭旧上下文
	if err := closeContext(conn, oldContextID); err != nil {
		return err
	}

	// 开启新上下文
	return sendTextInContext(conn, newResponse, newContextID, nil)
}

func endConversation(conn *websocket.Conn) error {
	msg := WSMessage{CloseSocket: true}
	return sendJSON(conn, msg)
}

// 辅助函数 =======================================================
func sendJSON(conn *websocket.Conn, data interface{}) error {
	payload, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("marshal error: %w", err)
	}

	if err := conn.WriteMessage(websocket.TextMessage, payload); err != nil {
		return fmt.Errorf("write error: %w", err)
	}
	return nil
}

func receiveMessages(ctx context.Context, conn *websocket.Conn, wg *sync.WaitGroup) {
	defer wg.Done()

	contextAudio := make(map[string]*bytes.Buffer)

	for {
		select {
		case <-ctx.Done():
			return
		default:
			_, message, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseNormalClosure) {
					log.Printf("WebSocket error: %v", err)
				}
				return
			}

			var resp APIResponse
			if err := json.Unmarshal(message, &resp); err != nil {
				log.Printf("JSON parse error: %v", err)
				continue
			}

			// 处理音频数据
			if resp.Audio != "" {
				audioData, err := base64.StdEncoding.DecodeString(resp.Audio)
				if err != nil {
					log.Printf("Base64 decode error: %v", err)
				} else {
					if _, exists := contextAudio[resp.ContextID]; !exists {
						contextAudio[resp.ContextID] = bytes.NewBuffer(nil)
					}
					contextAudio[resp.ContextID].Write(audioData)
					fmt.Printf("Received %d bytes of audio for context '%s'\n", len(audioData), resp.ContextID)
				}
			}

			// 处理上下文完成通知
			if resp.IsFinal {
				if buf, exists := contextAudio[resp.ContextID]; exists {
					fmt.Printf("Context '%s' completed with %d bytes of audio\n", resp.ContextID, buf.Len())
					delete(contextAudio, resp.ContextID)
				}
			}
		}
	}
}
