http_port: 5432
grpc_port: 5442
localhost: http://127.0.0.1:5432
#ingress_url: http://test-tts.aliyun-hk.airudderint.com
ingress_url: http://**************
services:
  addr: test-tts-grpc.aliyun-hk.airudderint.com:80
tts_v1:
  url: https://test-tts.airudder.com
zaplog:
  level: debug
  logpath: ./logs/
  logfile: ./data.log
  loglifetime: 720 #保存最近30天 保存小时
alarm:
  url: http://ares.aliyun-hk.airudderint.com/api/alarm
  notify_user: zhengxiang.zhu
  group_name: TTS (测试环境) 报警群

k8s:
  url: http://test-k8s-infra.aliyun-hk.airudderint.com/scaleZeroToOne
  replica: 4
  namespace: test

redis:
  cache:
    addr: localhost:6379
    password:
    db: 1

stream:
  chunk_us: 200000

cache_dir: /Users/<USER>/Gitlab/tts-service/audio/cache

cache:
  v2_address: [ { "region": "test", "address": "127.0.0.1:6379" } ]
  cache_duration: 24
  delete_duration: 24

elevenlabs:
  speakers: [ { "voice_id": "21m00Tcm4TlvDq8ikWAM","name": "Rachel" },{ "voice_id": "AZnzlk1XvdvUeBnXmlld","name": "Domi" },{ "voice_id": "EXAVITQu4vr4xnSDxMaL","name": "Bella" },{ "voice_id": "ErXwobaYiN019PkySvjV","name": "Antoni" },{ "voice_id": "MF3mGyEYCl7XYWbV9V6O","name": "Elli" },{ "voice_id": "TxGEqnHWrfWFTfGW9XjX","name": "Josh" },{ "voice_id": "VR6AewLTigWG4xSOukaG","name": "Arnold" },{ "voice_id": "pNInz6obpgDQGcFmaJgB","name": "Adam" },{ "voice_id": "yoZ06aMxZJJ28mfd3POQ","name": "Sam" },{ "voice_id": "2sXM5ziEnuJLRX2vM2Hi","name": "Jax" },{ "voice_id": "6qUShAi2a5rAqjnG7dVe","name": "Tally" },{ "voice_id": "97GpRKq8h6cWyM18WGPm","name": "MSTTS2" },{ "voice_id": "AaVJmDVNKvYgEvxAckyf","name": "YoungTeen" },{ "voice_id": "AqbKn0GYBobStpV7c0Uo","name": "MSTTS" },{ "voice_id": "CJ4yr9gREVi0AYgCODsI","name": "callcentercloned" },{ "voice_id": "CZx99q9RSCh2evGN9RpV","name": "Natalie" },{ "voice_id": "D9CCmRfxjTWe8sdpAqjq","name": "Dylan" },{ "voice_id": "Fwnbp9c3cCxJP46u9mA3","name": "Aaron" },{ "voice_id": "JT9GjvTlmmlHA9dTEXEs","name": "test1" },{ "voice_id": "NrwSzJGVWFJHO4mlIg95","name": "Joanne" },{ "voice_id": "OTwbfRwRnefea8LAZO9Y","name": "Alice" },{ "voice_id": "XBuXg0Wza8jEhZHZIAIl","name": "Caroline" },{ "voice_id": "Xo4uNYAe6uxM02NGggEd","name": "Travis" },{ "voice_id": "aksURVAkKaJA9Vc7ANH3","name": "MS-PH" },{ "voice_id": "cJpZ3eTtGqLaEHgyWJkj","name": "YoutubeShorts" },{ "voice_id": "dKaozETg7KXSct8zjKOf","name": "Raj" },{ "voice_id": "gxvpxWyDCYf9DbUCPkqv","name": "Valley" },{ "voice_id": "hEiKc7rdHwiW0MvGMGOI","name": "Myriam" },{ "voice_id": "kiS0bF5W5HLXhfJ87KBO","name": "USCallCenter1" },{ "voice_id": "kjzTZSe9raxYaIX19okA","name": "Maya" },{ "voice_id": "mwvmo4JBrbubq0fhb8OQ","name": "NigeriaMaleMobi" },{ "voice_id": "r3i1LICwWzz1VkAeuBgL","name": "Jonathon" },{ "voice_id": "r94Nw2XGgx3WNdOIxNAc","name": "William" },{ "voice_id": "ua6KDmPjGievquxPD0tA","name": "YoutubeTikTok" },{ "voice_id": "y9UliaGAQegdPzjj0QPQ","name": "Bo" },{ "voice_id": "zgs0syIbJEcqiSG9WyHG","name": "Myra" } ]
  locales: [ { "name": "en","model_id": "eleven_monolingual_v1" },{ "name": "ar","model_id": "eleven_multilingual_v2" },{ "name": "id","model_id": "eleven_multilingual_v2" },{ "name": "zh","model_id": "eleven_multilingual_v2" },{ "name": "fil","model_id": "eleven_multilingual_v2" },{ "name": "ms","model_id": "eleven_multilingual_v2" },{ "name": "default","model_id": "eleven_multilingual_v1" } ]
  elevenlabs_key: ********************************
  streaming_latency: 3
  stability: 100
  similarity_boost: 100

xunfei:
  #  host: https://tts-api-sg.xf-yun.com/v2/tts
  #  app_id: ga2990a0
  #  api_key: ********************************
  #  api_secret: 6afe6d1aed7965c1de0fc84f29f37fb2

  host: https://tts-api.xfyun.cn/v2/tts
  app_id: 99aec895
  api_key: ********************************
  api_secret: ZWY4MjI2MjQ4ODZiMjAxODhlNzI2MTJk