#!/usr/bin/env bash
# This file is part of the tts-service project.
# For more information, please refer to the relevant documents.
# Copyright 2020 Airudder.com
# Author: lixia<PERSON>
# The current script dir.
CURRENT=$(cd -P "$(dirname $0)" >/dev/null && pwd)
VERSION_C=$1
echo $VERSION_C
echo ${CURRENT}
cd ${CURRENT}
if [ "${VERSION_C}" == "" ]; then
  echo "not set version,use default version"
  VERSION_C=$(cat ${CURRENT}/VERSION)
  NEXT=$(echo ${VERSION_C} | awk -F "." '{print $3}')
  ((NEXT++))
  VERSION_N="$(echo ${VERSION_C} | awk -F "." '{print $1"."$2}').${NEXT}"
  echo ">>>> Upgrade Version: ${VERSION_C} --> ${VERSION_N}"
fi
# Current version.

VERSION_N=${VERSION_C}
echo ">>>> Upgrade Version: ${VERSION_C} --> ${VERSION_N}"

REGISTRY="airudder-registry.cn-hongkong.cr.aliyuncs.com"
# The tts-service database image tag.
TAG=${REGISTRY}/airudder/tts-service:${VERSION_N}

docker build -t ${TAG} -f ${CURRENT}/Dockerfile --no-cache --force-rm ${CURRENT}
docker image prune -f
docker images | grep tts-service
docker push ${TAG}
