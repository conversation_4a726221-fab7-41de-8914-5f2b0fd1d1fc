package tts_service

import (
	"context"
	"fmt"
	"google.golang.org/grpc"
	"io"
	"os"
	"testing"
	"time"
	"tts-service/pkg/pck2wav"
	"tts-service/rpc/microsoft/pb"
)

func TestMicrosoftTTS(t *testing.T) {
	conn, err := grpc.Dial("localhost:5433", grpc.WithInsecure())
	if err != nil {
		panic(err)
	}

	defer conn.Close()

	client := pb.NewMicrosoftTTSClient(conn)

	stream, err := client.Synthesis(context.Background(), &pb.MicrosoftTtsRequest{
		RequestId:        "bead5702892793ad48c8b78c24c3eed46",
		Provider:         "elevenlabs",
		Locale:           "eleven_monolingual_v1",
		Speaker:          "21m00Tcm4TlvDq8ikWAM",
		ElevenlabsKey:    "********************************",
		Stability:        15,
		SimilarityBoost:  20,
		StreamingLatency: 0,
		Ssml:             `Hello, the latest drone only costs US$300, and you can place an order directly in the online store, and it will arrive as soon as tomorrow 2023-05-25`,
		//Ssml: `<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US"><voice name="en-US-JennyMultilingualNeural">Thank you. Please check with me, is your ZIP code <say-as interpret-as='cardinal'>9</say-as> <say-as interpret-as='cardinal'>3</say-as> <say-as interpret-as='cardinal'>2</say-as> <say-as interpret-as='cardinal'>2</say-as> <say-as interpret-as='cardinal'>0</say-as> ?</voice></speak>`,
	})

	if err != nil {
		panic(err)
	}

	now := time.Now()
	audioContent := make([]byte, 0)
	for {
		feature, err := stream.Recv()
		if err == io.EOF {
			break
		}

		if err != nil {
			panic(err)
		}

		fmt.Printf("Received %d, %d, %v, %d, %s, %d\n", time.Now().Sub(now).Milliseconds(), feature.Duration, feature.IsEnd, feature.Code, feature.Status, len(feature.AudioContent))
		audioContent = append(audioContent, feature.AudioContent...)
		now = time.Now()
	}

	z, _ := pck2wav.Pcm2Wav(audioContent, 8000)
	file, _ := os.OpenFile("/Users/<USER>/Desktop/audio_pcm_latency_0.wav", os.O_WRONLY|os.O_CREATE, 0666)

	defer file.Close()
	_, _ = file.Write(z)

	_ = file.Sync()
}
