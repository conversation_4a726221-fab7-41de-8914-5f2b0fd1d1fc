package main

import (
	"github.com/judwhite/go-svc"
	"syscall"
	"tts-service/internal/pkg/alarm"
	"tts-service/internal/pkg/code"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service"
)

func main() {
	service := &tts_service.TTSService{}
	// 服务启动流程
	if err := svc.Run(service, syscall.SIGINT, syscall.SIGTERM); err != nil {
		log.Errorf(service.Context, "tts service panic %s", err.Error())

		alarm.P2WithContent(
			service.Context,
			"TTS-Service服务异常关闭",
			code.ErrorInternal,
			alarm.NewPairs("关闭原因", err.Error()))

		panic(err.Error())
	}
}
