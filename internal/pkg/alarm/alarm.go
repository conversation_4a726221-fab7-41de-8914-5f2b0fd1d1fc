package alarm

import (
	"context"
	"fmt"
	"github.com/prometheus/client_golang/prometheus"
	"strconv"
	"strings"
	"tts-service/internal/pkg/code"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/constants"
	"tts-service/internal/pkg/log"
	"tts-service/internal/pkg/metrics"
	"tts-service/internal/tts_service/models"
	"tts-service/pkg/alarm"
)

var ignoreErrorSets = map[int]struct{}{
	code.ErrorContainer:       {},
	code.ErrorContextCanceled: {},
}

// Pairs 告警字段内容
type Pairs struct {
	Key   string
	Value string
}

// NewPairs 返回一个告警字段内容
func NewPairs(key, value string) Pairs {
	return Pairs{
		Key:   key,
		Value: value,
	}
}

func (pairs *Pairs) String() string {
	// 不存在值的情况下，直接返回空
	if pairs.Value != "" {
		return fmt.Sprintf("%s：%s", pairs.Key, pairs.Value)
	}

	return ""
}

// 将 paris 列表转换一个显示文本
func convertPairsToBody(pairs []Pairs) string {
	texts := make([]string, 0, len(pairs))
	for index := range pairs {
		text := pairs[index].String()
		if text != "" {
			texts = append(texts, text)
		}
	}

	return strings.Join(texts, "\n")
}

func P1(ctx context.Context, request *models.TTSRequest, errorCode int, message string, notifyCompany bool) {
	alarmFeishu(ctx, request, errorCode, message, notifyCompany, 1)
}

func P3(ctx context.Context, request *models.TTSRequest, errorCode int, message string, notifyCompany bool) {
	alarmFeishu(ctx, request, errorCode, message, notifyCompany, 3)
}

func alarmFeishu(ctx context.Context, request *models.TTSRequest, errorCode int, message string, notifyCompany bool, level int) {
	log.Errorf(ctx, "请求生成失败：%d, %s", errorCode, message)
	// 忽略掉部分错误码
	if _, ok := ignoreErrorSets[errorCode]; ok {
		return
	}

	text := request.Text
	if len(request.Texts) > 0 {
		text = strings.Join(request.Texts, "+")
	}

	if len(message) > 200 {
		message = message[:200]
	}

	metrics.RequestAlarmCallCounter.With(prometheus.Labels{
		"tts_type":    request.TTSType,
		"server_name": request.ServerName,
		"detail":      code.GetCodeDescription(errorCode),
		"error_code":  strconv.Itoa(errorCode),
	}).Inc()

	pairs := []Pairs{
		NewPairs("", message),
		NewPairs("Error Code", strconv.Itoa(errorCode)),
		NewPairs("Error Detail", code.GetCodeDescription(errorCode)),
		NewPairs("CallId", request.CallId),
		NewPairs("Robot Name", request.RobotName),
		NewPairs("Company", request.Company),
		NewPairs("Mobile Phone", request.PhoneNumber),
		NewPairs("TTS Type", request.TTSType),
		NewPairs("Speaker", request.Speaker),
		NewPairs("Locale", request.Locale),
		NewPairs("Text", text),
		NewPairs("Account Type", request.AccountType),
		NewPairs("Server Name", request.ServerName),
	}

	// 是否需要通知到公司 csm，理论上非重复请求都需要通知 csm
	company := ""
	if notifyCompany {
		company = request.Company
	}

	notifyUser := config.Config.GetString("alarm.notify_user")
	groupName := config.Config.GetString("alarm.group_name")
	if request.TTSType == constants.AirudderV2Provider && code.IsContainerError(errorCode) {
		notifyUser = config.Config.GetString("alarm.notify_ai_user")
	}

	if errorCode == code.RealtimeErrorContainer || errorCode == code.RealtimeErrorInternal {
		notifyUser = config.Config.GetString("alarm.notify_schedule_user")
		groupName = config.Config.GetString("alarm.group_schedule_name")
	}

	if request.CallId != "" {
		message = fmt.Sprintf("%s - %s", message, request.CallId)
	}

	err := alarm.GetAlarmClient().Alarm(
		config.Config.GetString("alarm.url"),
		notifyUser,
		message,
		convertPairsToBody(pairs),
		groupName,
		company,
		level,
	)

	if err != nil {
		log.Errorf(ctx, "alarm error: %s", err.Error())
	}
}

func P2WithContent(ctx context.Context, message string, errorCode int, pairs ...Pairs) {
	pairs = append(pairs, NewPairs("错误码", strconv.Itoa(errorCode)))

	err := alarm.GetAlarmClient().Alarm(
		config.Config.GetString("alarm.url"),
		config.Config.GetString("alarm.notify_user"),
		message,
		convertPairsToBody(pairs),
		config.Config.GetString("alarm.group_name"),
		"",
		3,
	)

	if err != nil {
		log.Errorf(ctx, "alarm error: %s", err.Error())
	}
}
