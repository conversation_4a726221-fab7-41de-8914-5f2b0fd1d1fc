package code

const (
	// ServerOK 返回成功
	ServerOK = 200

	// ErrorParams 参数错误
	ErrorParams = 422001
	// ErrorMissingParams 参数缺失
	ErrorMissingParams = 422002
	// ErrorSpeak 不支持的音色
	ErrorSpeak = 422102
	// ErrorQuota 超过限额
	ErrorQuota = 429001
	// ErrorTimeout 请求超时
	ErrorTimeout = 504001
	// ErrorInternal 内部错误
	ErrorInternal = 500001
	// ErrorRequestInternal 内部调用错误
	ErrorRequestInternal   = 500002
	ContainerInternalError = 500003
	// ErrorContainer 容器不可用
	ErrorContainer = 503001
	// ErrorRouter 不支持的路由
	ErrorRouter = 502001
	// ErrorContextCanceled 链接断开
	ErrorContextCanceled = 504001

	// 实时TTS 特地的错误，用来发送给调度
	RealtimeErrorContainer = 1503001
	RealtimeErrorInternal  = 1500001
)

var errorCodeDescription = map[int]string{
	ErrorParams:            "Error Params",
	ErrorMissingParams:     "Missing Params",
	ErrorSpeak:             "Unsupported speaker or syntax error",
	ErrorQuota:             "Limit Exceeded",
	ErrorTimeout:           "Request Timeout",
	ErrorInternal:          "Internal Error",
	ErrorRequestInternal:   "Internal Request error",
	ErrorContainer:         "Container Unavailable",
	ContainerInternalError: "Container internal error",
	ErrorRouter:            "Unsupported Route",
}

var needRepeatedCodes = map[int]struct{}{
	ErrorQuota:           {},
	ErrorTimeout:         {},
	ErrorInternal:        {},
	ErrorRequestInternal: {},
	ErrorContainer:       {},
}

var containerError = map[int]struct{}{
	ErrorInternal:          {},
	ContainerInternalError: {},
}

func GetCodeDescription(code int) string {
	if description, ok := errorCodeDescription[code]; ok {
		return description
	}

	return ""
}

func CheckCodeNeedRepeated(errorCode int) bool {
	_, ok := needRepeatedCodes[errorCode]
	return ok
}

func IsContainerError(code int) bool {
	_, ok := containerError[code]
	return ok
}
