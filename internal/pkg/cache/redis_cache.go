package cache

import (
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"time"
	"tts-service/internal/pkg/config"
)

type redisClient struct {
	client *redis.Client
}

var Client redisClient

// InitRedisCacheClient 初始化 redis 缓存
func InitRedisCacheClient() {
	if config.Config.GetString("redis.cache.addr") != "" {
		Client = redisClient{
			client: redis.NewClient(&redis.Options{
				Addr:     config.Config.GetString("redis.cache.addr"),
				Password: config.Config.GetString("redis.cache.password"),
				DB:       config.Config.GetInt("redis.cache.db"),
			}),
		}
	}
}
func (client *redisClient) IsActive() bool {
	return client.client != nil && !config.Config.GetBool("disable_cache")
}

func (client *redisClient) Set(key string, value interface{}, expiration time.Duration) error {
	ctx := context.Background()
	if client.IsActive() {
		content, err := json.Marshal(value)
		if err != nil {
			return err
		}
		return client.client.Set(ctx, key, content, expiration).Err()
	}

	return nil
}

func (client *redisClient) Get(key string) ([]byte, error) {
	ctx := context.Background()
	if client.IsActive() {
		command := client.client.Get(ctx, key)
		// 返回数据是否为空
		if command.Err() == redis.Nil {
			return nil, nil
		}
		return command.Bytes()
	}

	return nil, nil
}

func (client *redisClient) Expired(key string, duration time.Duration) error {
	ctx := context.Background()
	if client.IsActive() {
		return client.client.Expire(ctx, key, duration).Err()
	}

	return nil
}

func (client *redisClient) Delete(args ...string) error {
	ctx := context.Background()
	if client.IsActive() {
		return client.client.Del(ctx, args...).Err()
	}

	return nil
}
