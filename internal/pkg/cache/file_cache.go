package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"syscall"
	"time"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/log"
	"tts-service/internal/pkg/utils"
	"tts-service/pkg/access_time"
)

type FileCache struct {
	Filename string
	CallId   string
	Value    interface{}
}

type fileClient struct {
	Dir    string
	cacheC chan FileCache
}

var FileClient fileClient

func (client *fileClient) readFileWithTimeout(filename string, timeout time.Duration) ([]byte, error) {
	filename = fmt.Sprintf("%s/%s", client.Dir, filename)

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	ch := make(chan error, 1)
	var result []byte

	go func() {
		content, err := os.ReadFile(filename)
		if err != nil && os.IsNotExist(err) {
			ch <- nil
			return
		}

		if err != nil {
			ch <- err
		}

		_ = os.Chtimes(filename, time.Now(), time.Now())
		result = content
		ch <- nil
	}()

	select {
	case err := <-ch:
		return result, err
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

func InitFileCacheClient() {
	dir := "/audio/cache"
	if config.Config.GetString("cache_dir") != "" {
		dir = config.Config.GetString("cache_dir")
	}

	_ = os.MkdirAll(dir, 0777)

	FileClient = fileClient{Dir: dir, cacheC: make(chan FileCache, 10000)}
	go FileClient.loopForWriteFile()
	// go FileClient.loopDeleteFile()
}

// 使用一个 ticker 定时删除长期未访问的数据
func (client *fileClient) loopDeleteFile() {
	ctx := context.Background()
	ticker := time.NewTicker(config.DeleteDuration)
	for {
		// 小于这个日期的文件都会被直接删除
		timeoutDuration := time.Now().Add(config.CacheDuration)
		err := filepath.Walk(client.Dir, func(path string, info fs.FileInfo, err error) error {
			if err != nil {
				return err
			}

			if info.IsDir() {
				return nil
			}

			if stat, ok := info.Sys().(*syscall.Stat_t); ok {
				accessTime := access_time.AccessTime(stat)
				// 上次访问时间小于给定的时间
				if accessTime.Before(timeoutDuration) {
					log.Infof(ctx, "clear cache file: path=%s, time=%s", path, accessTime)
					if err = os.Remove(path); err != nil {
						return err
					}
				}
			}

			return nil
		})

		if err != nil {
			log.Errorf(ctx, "walk filepath err: %s", err.Error())
		}

		<-ticker.C
	}
}

func (client *fileClient) loopForWriteFile() {
	ctx := context.Background()
	for cache := range client.cacheC {
		ctx = context.WithValue(ctx, "call_id", cache.CallId)
		content, err := json.Marshal(cache.Value)
		if err != nil {
			log.Infof(ctx, "marshal file error：%s", err.Error())
			continue
		}
		filename := fmt.Sprintf("%s/%s", client.Dir, cache.Filename)
		if err = os.WriteFile(filename, content, 0666); err != nil {
			log.Infof(ctx, "write to file error：%s", err.Error())
		} else {
			log.Infof(ctx, "文件存储成功：%s", filename)
		}
	}
}

func (client *fileClient) getFilename(key, specialKey string) string {
	return fmt.Sprintf("%s.filecache", utils.Md5Join(key, specialKey))
}

func (client *fileClient) getFilenameV2(key string) string {
	return fmt.Sprintf("%s.v2cache", utils.Md5Join(key))
}

func (client *fileClient) GetV2(key string) ([]byte, string, error) {
	filename := client.getFilenameV2(key)

	content, err := client.readFileWithTimeout(filename, 100*time.Millisecond)
	return content, filename, err
}

func (client *fileClient) Get(key string, specialKey string) ([]byte, string, error) {
	if config.DisableCache {
		return nil, "", nil
	}

	filename := client.getFilename(key, specialKey)
	content, err := client.readFileWithTimeout(filename, 100*time.Millisecond)
	return content, filename, err
}

func (client *fileClient) Delete(filename string) error {
	filename = fmt.Sprintf("%s/%s", client.Dir, filename)
	return os.Remove(filename)
}

func (client *fileClient) Set(key string, specialKey string, callId string, value interface{}) {
	if config.DisableCache {
		return
	}

	client.cacheC <- FileCache{
		Filename: client.getFilename(key, specialKey),
		CallId:   callId,
		Value:    value,
	}
}

func (client *fileClient) SetV2(key string, value interface{}) {
	client.cacheC <- FileCache{
		Filename: client.getFilenameV2(key),
		Value:    value,
	}
}
