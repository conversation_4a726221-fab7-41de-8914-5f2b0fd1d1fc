package db

import (
	"github.com/pkg/errors"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"time"
)

type TTSCache struct {
	ServerName string `gorm:"column:server_name"`
	Speaker    string `gorm:"column:speaker"`
	Locale     string `gorm:"column:locale"`
	Keywords   string `gorm:"column:keywords"`
	Version    int64  `gorm:"column:version"`
}

func (TTSCache) TableName() string {
	return "tts_cache_delete"
}

func getRobotDb(address string) (*gorm.DB, error) {
	//dsn := "rm_platform_rw:ryUc169JFAPLozZJ@tcp(rm-3nsw2dkg08vqkm7md.mysql.rds.aliyuncs.com:3306)/irobot?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(address), &gorm.Config{})
	if err != nil {
		return nil, errors.Wrap(err, "数据库链接错误")
	}

	return db, nil
}

func GetTTSCache(address string) []TTSCache {
	db, err := getRobotDb(address)
	if err != nil {
		return nil
	}

	var ttsCache []TTSCache
	db.Where("version >= ?", time.Now().AddDate(0, -1, 0).UnixMilli()).Find(&ttsCache)

	return ttsCache
}
