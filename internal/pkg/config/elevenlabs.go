package config

import (
	"encoding/json"
	"fmt"
	"strings"
)

type ElevenlabsKey struct {
	Key      string `json:"key"`
	Speakers string `json:"speakers"`
}

type ElevenlabsSpeaker struct {
	Name    string `json:"name"`
	VoiceId string `json:"voice_id"`
}

type ElevenlabsLocale struct {
	Name    string `json:"name"`
	ModelId string `json:"model_id"`
}

var (
	SpeakerVoiceMaps             map[string]string
	LocaleVoiceMaps              map[string]string
	SpeakerKeyMaps               map[string]string
	V2LocaleVoiceMaps            map[string]string
	ElevenlabsMultiContextConfig map[string]any

	StreamingLatency     int64 = 0
	Stability            int64 = 0
	SimilarityBoost      int64 = 0
	ElevenlabsDefaultKey       = ""
	elevenlabsV2Locales        = ""
)

func UpdateElevenlabsConfig() {
	//[{
	//	"name": "xx",
	//	"voice_id": "xx",
	//}]
	speakMapStr := Config.GetString("elevenlabs.speakers")
	localeMapStr := Config.GetString("elevenlabs.locales")
	elevenlabsKeyMapStr := Config.GetString("elevenlabs.elevenlabs_key_maps")
	StreamingLatency = Config.GetInt64("elevenlabs.streaming_latency")
	Stability = Config.GetInt64("elevenlabs.stability")
	SimilarityBoost = Config.GetInt64("elevenlabs.similarity_boost")
	ElevenlabsDefaultKey = Config.GetString("elevenlabs.elevenlabs_key")
	elevenlabsV2Locales = Config.GetString("elevenlabs.v2_locales")

	voiceMaps := make(map[string]string, 0)
	if speakMapStr != "" {
		var speakers []ElevenlabsSpeaker
		if err := json.Unmarshal([]byte(speakMapStr), &speakers); err != nil {
			fmt.Printf("Update elevenlabs speaker error: %s\n", err.Error())
		} else {
			for index := range speakers {
				voiceMaps[strings.ToLower(speakers[index].Name)] = speakers[index].VoiceId
			}
		}
	}
	SpeakerVoiceMaps = voiceMaps

	localeMaps := make(map[string]string, 0)
	if localeMapStr != "" {
		var locales []ElevenlabsLocale
		if err := json.Unmarshal([]byte(localeMapStr), &locales); err != nil {
			fmt.Printf("Update elevenlabs locale error: %s\n", err.Error())
		} else {
			for index := range locales {
				localeMaps[strings.ToLower(locales[index].Name)] = locales[index].ModelId
			}
		}
	}

	LocaleVoiceMaps = localeMaps

	keyMaps := make(map[string]string, 0)
	if elevenlabsKeyMapStr != "" {
		var keys []ElevenlabsKey
		if err := json.Unmarshal([]byte(elevenlabsKeyMapStr), &keys); err != nil {
			fmt.Printf("Update elevenlabs key error: %s\n", err.Error())
		} else {
			for _, key := range keys {
				for _, speaker := range strings.Split(key.Speakers, ",") {
					keyMaps[strings.ToLower(speaker)] = key.Key
				}
			}
		}
	}

	SpeakerKeyMaps = keyMaps

	v2LocaleMaps := make(map[string]string, 0)
	if elevenlabsV2Locales != "" {
		var locales []ElevenlabsLocale
		if err := json.Unmarshal([]byte(elevenlabsV2Locales), &locales); err != nil {
			fmt.Printf("Update elevenlabs locale error: %s\n", err.Error())
		} else {
			for index := range locales {
				v2LocaleMaps[strings.ToLower(locales[index].Name)] = locales[index].ModelId
			}
		}
	}

	V2LocaleVoiceMaps = v2LocaleMaps

	elevenlabsMultiContextConfig := make(map[string]any)
	if configStr := Config.GetString("elevenlabs_multi_context"); configStr != "" {
		var customConfigs map[string]any
		if err := json.Unmarshal([]byte(configStr), &customConfigs); err != nil {
			fmt.Printf("elevenlabsMultiContextConfig json unmarshal error: %s\n", err.Error())
		} else {
			elevenlabsMultiContextConfig = customConfigs
		}
	}
	ElevenlabsMultiContextConfig = elevenlabsMultiContextConfig
}
