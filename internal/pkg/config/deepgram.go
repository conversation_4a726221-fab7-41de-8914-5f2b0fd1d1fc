package config

import (
	"encoding/json"
	"fmt"
	"strings"
)

var (
	DeepgramAuthKey   string
	DeepgramVoiceMaps map[string]string
)

func UpdateDeepgramConfig() {
	DeepgramAuthKey = ""
	if authKey := Config.GetString("deepgram.auth_key"); authKey != "" {
		DeepgramAuthKey = fmt.Sprintf("Token %s", authKey)
	}

	DeepgramVoiceMaps = make(map[string]string)
	if deepgramVoiceKey := Config.GetString("deepgram.deepgram_voice_key"); deepgramVoiceKey != "" {
		deepgramVoiceMaps := make(map[string]string)
		if err := json.Unmarshal([]byte(deepgramVoiceKey), &deepgramVoiceMaps); err != nil {
			fmt.Printf("Update deepgram voice error: %s\n", err.<PERSON>rror())
		} else {
			for key, value := range deepgramVoiceMaps {
				DeepgramVoiceMaps[strings.ToLower(key)] = strings.ToLower(value)
			}
		}
	}
}
