package config

var (
	AliyunAccessKeyId     string
	AliyunAccessKeySecret string
	AliyunAppKey          string

	AliyunTokenDomain  = "nls-meta.cn-shanghai.aliyuncs.com"
	AliyunTokenRegion  = "cn-shanghai"
	AliyunTokenAction  = "CreateToken"
	AliyunTokenVersion = "2019-02-28"
	AliyunAddress      = "wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1"
	AliyunNamespace    = "FlowingSpeechSynthesizer"
)

func UpdateAliyunConfig() {
	if accessKeyId := Config.GetString("aliyun.access_key_id"); accessKeyId != "" {
		AliyunAccessKeyId = accessKeyId
	}

	if accessKeySecret := Config.GetString("aliyun.access_key_secret"); accessKeySecret != "" {
		AliyunAccessKeySecret = accessKeySecret
	}

	if appKey := Config.GetString("aliyun.app_key"); appKey != "" {
		AliyunAppKey = appKey
	}

	if address := Config.GetString("aliyun.address"); address != "" {
		AliyunAddress = address
	}

	if namespace := Config.GetString("aliyun.namespace"); namespace != "" {
		AliyunNamespace = namespace
	}

	if tokenDomain := Config.GetString("aliyun.token_domain"); tokenDomain != "" {
		AliyunTokenDomain = tokenDomain
	}

	if tokenRegion := Config.GetString("aliyun.token_region"); tokenRegion != "" {
		AliyunTokenRegion = tokenRegion
	}

	if tokenAction := Config.GetString("aliyun.token_action"); tokenAction != "" {
		AliyunTokenAction = tokenAction
	}

	if tokenVersion := Config.GetString("aliyun.token_version"); tokenVersion != "" {
		AliyunTokenVersion = tokenVersion
	}
}
