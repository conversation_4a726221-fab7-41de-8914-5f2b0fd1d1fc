package config

var (
	MiniMaxHost      string
	MiniMaxGroupID   string
	MiniMaxApiKey    string
	MinimaxVoiceMaps map[string]string
)

func UpdateMiniMaxConfig() {
	if authKey := Config.GetString("minimax.group_id"); authKey != "" {
		MiniMaxGroupID = authKey
	}

	if model := Config.GetString("minimax.api_key"); model != "" {
		MiniMaxApiKey = model
	}

	if host := Config.GetString("minimax.host"); host != "" {
		MiniMaxHost = host
	}

	MinimaxVoiceMaps = Config.GetStringMapString("minimax.minimax_voice_key")
}
