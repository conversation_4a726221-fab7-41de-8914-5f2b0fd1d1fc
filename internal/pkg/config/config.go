package config

import (
	"github.com/pkg/errors"
	"github.com/shima-park/agollo"
	"github.com/spf13/viper"
	"log"
	"os"
)

const EnvFlag = "TTS_ENV"

var (
	Config = viper.New()
)

type UpdateConfig func()

func GetEnvByFlag() string {
	return os.Getenv(EnvFlag)
}

// InitConfig 从配置文件中初始化数据
func InitConfig(path string) error {
	Config.AddConfigPath(path)
	Config.SetConfigName("config")
	if err := Config.ReadInConfig(); err != nil {
		return err
	}

	Config.WatchConfig()
	return nil
}

// InitConfigFromApollo 从 Apolllo 中初始化数据
func InitConfigFromApollo(afterConfigs ...UpdateConfig) error {
	Config.SetConfigType("yaml")
	appName := os.Getenv("App")
	accessKey := os.Getenv("AccessKey")
	cluster := os.Getenv("Cluster")
	agoIns, err := agollo.New("", appName, agollo.Cluster(cluster),
		agollo.AccessKey(accessKey), agollo.FailTolerantOnBackupExists(), agollo.AutoFetchOnCacheMiss())

	if err != nil {
		return errors.Wrap(err, "init apollo client failed")
	}

	updateConfigs := func() {
		for key, value := range agoIns.GetNameSpace("application") {
			Config.Set(key, value.(string))
		}

		for _, afterConfig := range afterConfigs {
			afterConfig()
		}
	}

	updateConfigs()

	errorCh := agoIns.Start()
	watchCh := agoIns.Watch()

	go func() {
		for {
			select {
			case err := <-errorCh:
				log.Printf("get resp from apollo server:%s\n", err.Err)
			case resp := <-watchCh:
				if resp.Error == nil {
					updateConfigs()
				} else {
					log.Printf("get resp from apollo server:%s\n", resp.Error.Error())
				}
			}
		}
	}()

	return nil
}
