package config

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"
	"tts-service/internal/pkg/db"
	"tts-service/internal/pkg/utils"
)

type CustomConfig struct {
	SpeakingRate float64 `json:"speaking_rate"`
}

var (
	DeleteDuration          time.Duration
	CacheDuration           time.Duration
	DisableCache            bool
	DisableRobotCaches      map[string]struct{}
	DisableServerNameCaches map[string]struct{}

	RobotCustomVersion map[string]string

	EnableCacheV2Robots map[string]struct{}

	CacheV2Address    map[string]string
	SpeakerReplaceMap map[string]string

	CacheSpeakerReplaceMap map[string]int64
	CacheLocaleKeywordsMap map[string]map[string]int64

	RobotCustomConfig map[string]CustomConfig
)

type CacheAddress struct {
	Region  string `json:"region"`
	Address string `json:"address"`
}

type CacheReplace struct {
	ServerName     string `json:"server_name"`
	Speaker        string `json:"speaker"`
	ReplaceSpeaker string `json:"replace_speaker"`
}

func UpdateCacheConfig() {
	DeleteDuration = time.Duration(Config.GetInt("cache.delete_duration")) * time.Hour
	CacheDuration = time.Duration(-Config.GetInt("cache.cache_duration")) * time.Hour

	DisableCache = Config.GetBool("disable_cache")
	DisableRobotCaches = make(map[string]struct{})
	if robotCache := Config.GetString("cache.disable_robot"); robotCache != "" {
		for _, robot := range strings.Split(robotCache, ",") {
			DisableRobotCaches[robot] = struct{}{}
		}
	}

	DisableServerNameCaches = make(map[string]struct{})
	if serverNameCache := Config.GetString("cache.disable_server_name"); serverNameCache != "" {
		for _, serverName := range strings.Split(serverNameCache, ",") {
			DisableServerNameCaches[serverName] = struct{}{}
		}
	}

	fmt.Println("Update cache config success")

	EnableCacheV2Robots = make(map[string]struct{})
	if robotCache := Config.GetString("cache.enable_v2_robot"); robotCache != "" {
		for _, robot := range strings.Split(robotCache, ",") {
			EnableCacheV2Robots[robot] = struct{}{}
		}
	}

	CacheV2Address = make(map[string]string)
	if cacheV2Address := Config.GetString("cache.v2_address"); cacheV2Address != "" {
		var address []CacheAddress
		if err := json.Unmarshal([]byte(cacheV2Address), &address); err != nil {
			fmt.Printf("cache v2 address json unmarshal error: %s\n", err.Error())
		} else {
			for _, addressItem := range address {
				CacheV2Address[addressItem.Region] = addressItem.Address
			}
		}
	}

	RobotCustomVersion = make(map[string]string)
	if robotCustomVersionStr := Config.GetString("cache.robot_custom_version"); robotCustomVersionStr != "" {
		if err := json.Unmarshal([]byte(robotCustomVersionStr), &RobotCustomVersion); err != nil {
			fmt.Printf("cache v2 robot custom v ersion json unmarshal error: %s\n", err.Error())
			RobotCustomVersion = make(map[string]string)
		}
	}

	tempSpeakerReplaceMap := make(map[string]string)
	if replaceMapStr := Config.GetString("cache.speaker_replace_map"); replaceMapStr != "" {
		var replaceMap []CacheReplace
		if err := json.Unmarshal([]byte(replaceMapStr), &replaceMap); err != nil {
			fmt.Printf("cache speaker replace map json unmarshal error: %s\n", err.Error())
		} else {
			for _, replaceItem := range replaceMap {
				key := fmt.Sprintf("%s_%s", replaceItem.ServerName, replaceItem.Speaker)
				tempSpeakerReplaceMap[key] = replaceItem.ReplaceSpeaker
			}
		}
	}
	SpeakerReplaceMap = tempSpeakerReplaceMap

	customConfig := make(map[string]CustomConfig)
	if customConfigStr := Config.GetString("robot_custom_config"); customConfigStr != "" {
		var customConfigs map[string]CustomConfig
		if err := json.Unmarshal([]byte(customConfigStr), &customConfigs); err != nil {
			fmt.Printf("cache robot custom config json unmarshal error: %s\n", err.Error())
		} else {
			customConfig = customConfigs
		}
	}
	RobotCustomConfig = customConfig
}

func GetRobotSpeakingRate(robotName string, defaultValue float64) float64 {
	if customConfig, ok := RobotCustomConfig[robotName]; ok {
		return customConfig.SpeakingRate
	}
	return defaultValue
}

// GetReplaceSpeaker 根据 ServerName 和 Speaker 获取替换后的 Speaker
func GetReplaceSpeaker(serverName, speaker string) string {
	key := fmt.Sprintf("%s_%s", serverName, speaker)
	if replaceSpeaker, ok := SpeakerReplaceMap[key]; ok {
		return replaceSpeaker
	}
	if replaceVersion, ok := CacheSpeakerReplaceMap[key]; ok {
		return fmt.Sprintf("%s_%d", speaker, replaceVersion)
	}

	return speaker
}

func GetReplaceText(locale string, text string) string {
	if localeKeywords, ok := CacheLocaleKeywordsMap[locale]; ok {
		texts := strings.Split(text, "+")
		for _, row := range texts {
			if strings.HasSuffix(row, ".wav") || strings.HasSuffix(row, ".mp3") {
				continue
			}

			rowText := utils.ClearSSMLText(row)
			for word, version := range localeKeywords {
				if strings.Contains(rowText, word) {
					fmt.Printf("replace word: %s, version: %d\n", word, version)
					text = strings.ReplaceAll(text, word, fmt.Sprintf("%s_%d", word, version))
				}
			}
		}
		return text
	}

	return text
}

func ReloadCacheMap() {
	caches := db.GetTTSCache(Config.GetString("cache.db"))
	fmt.Printf("reload cache map, cache size: %d\n", len(caches))

	speakerReplaceMap := make(map[string]int64)
	localeKeywordsMap := make(map[string]map[string]int64)

	for _, cache := range caches {
		if cache.ServerName != "" && cache.Speaker != "" {
			key := fmt.Sprintf("%s_%s", cache.ServerName, cache.Speaker)
			speakerReplaceMap[key] = cache.Version
		} else if cache.Locale != "" && cache.Keywords != "" {
			if _, ok := localeKeywordsMap[cache.Locale]; !ok {
				localeKeywordsMap[cache.Locale] = make(map[string]int64)
			}
			for _, keyword := range strings.Split(cache.Keywords, "+") {
				localeKeywordsMap[cache.Locale][keyword] = cache.Version
			}
		}
	}

	CacheSpeakerReplaceMap = speakerReplaceMap
	CacheLocaleKeywordsMap = localeKeywordsMap
}
