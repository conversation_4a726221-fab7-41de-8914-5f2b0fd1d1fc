package config

var (
	XunfeiHost      string
	XunfeiAppID     string
	XunfeiApiKey    string
	XunfeiApiSecret string
)

func UpdateXunfeiConfig() {
	if authKey := Config.GetString("xunfei.app_id"); authKey != "" {
		XunfeiAppID = authKey
	}

	if model := Config.GetString("xunfei.api_key"); model != "" {
		XunfeiApiKey = model
	}
	if model := Config.GetString("xunfei.api_secret"); model != "" {
		XunfeiApiSecret = model
	}

	if host := Config.GetString("xunfei.host"); host != "" {
		XunfeiHost = host
	}
}
