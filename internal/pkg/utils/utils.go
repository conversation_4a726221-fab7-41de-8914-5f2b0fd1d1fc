package utils

import (
	"crypto/md5"
	"encoding/hex"
	"github.com/shopspring/decimal"
	"strings"
	"tts-service/pkg/data"
)

func Md5Join(texts ...string) string {
	res := make([]string, 0, len(texts))
	for _, text := range texts {
		if text == "" {
			continue
		}
		h := md5.New()
		h.Write([]byte(text))
		res = append(res, hex.EncodeToString(h.Sum(nil)))
	}

	return strings.Join(res, "-")
}

func GetLanguageCode(robotLanguage string) string {
	language := strings.Replace(strings.ToLower(robotLanguage), " ", "", -1)
	if code, ok := data.LanguageMap[language]; ok {
		return code
	}
	return ""
}

func GetStandardLanguage(code string) string {
	language := ""
	for k, v := range data.LanguageMap {
		if v == code {
			language = k
			break
		}
	}

	//首字母大写
	if len(language) > 0 {
		language = strings.ToUpper(language[:1]) + language[1:]
	}

	return language
}

func IsEmptyJsonString(s string) bool {
	if s == "" || s == "{}" || s == "[]" {
		return true
	}
	return false
}

func Float32To64(f float32) float64 {
	temp := decimal.NewFromFloat32(f)
	return temp.InexactFloat64()
}
