package utils

import "testing"

func TestFloat32To64(t *testing.T) {
	type args struct {
		f float32
	}
	tests := []struct {
		name string
		args args
		want float64
	}{
		{
			name: "test1",
			args: args{
				f: 1.0,
			},
			want: 1.0,
		},
		{
			name: "test1",
			args: args{
				f: 0,
			},
			want: 0,
		},
		{
			name: "test1",
			args: args{
				f: 1.2,
			},
			want: 1.2,
		},
		{
			name: "test2",
			args: args{
				f: 1.200000,
			},
			want: 1.2,
		},
		{
			name: "test3",
			args: args{
				f: 6,
			},
			want: 6,
		},
		{
			name: "test5",
			args: args{
				f: 0.71,
			},
			want: 0.71,
		},

		{
			name: "test5",
			args: args{
				f: 0.7,
			},
			want: 0.7,
		},
		{
			name: "test5",
			args: args{
				f: -16.7,
			},
			want: -16.7,
		},
		{
			name: "test6",
			args: args{
				f: 100.0,
			},
			want: 100.0,
		},
		{
			name: "test6",
			args: args{
				f: 3333344.0,
			},
			want: 3333344.0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Float32To64(tt.args.f); got != tt.want {
				t.Errorf("Float32To64() = %v, want %v", got, tt.want)
			}
		})
	}
}
