package environment

const LocalEnvironment Environment = "local" // 本地环境
const DevEnvironment Environment = "dev"     // 开发环境（默认的运行环境）
const TestEnvironment Environment = "test"   // 测试环境

var Env = DevEnvironment // 当前运行时环境（默认为开发环境）

type Environment string

// 获取全局运行环境字符串形式
func (e Environment) String() string { return string(e) }

// 检查当前全局运行环境是否是给定的值
func (e Environment) is(env Environment) bool { return e == env }

// Set 设置环境变量
func (e Environment) Set(env string) {
	if env != "" {
		Env = Environment(env)
	}
}

// IsDev 检查当前全局运行环境是否是开发环境
func (e Environment) IsDev() bool { return e.is(DevEnvironment) }

func (e Environment) IsLocal() bool { return e.is(LocalEnvironment) }

func (e Environment) IsTest() bool { return e.is(TestEnvironment) }
