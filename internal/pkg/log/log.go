package log

import (
	"context"
	"fmt"
	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"net"
	"os"
	"path"
	"tts-service/internal/pkg/config"
)

var Log = &ZapLogger{}
var HostName string

type ZapLogger struct {
	log  *zap.SugaredLogger
	Sync func() error
}

// InitLogger 配置zap日志,将zap日志库引入
func InitLogger() {
	var (
		core       zapcore.Core
		logLevel   string
		logPath    string
		logFile    string
		lifeTime   int
		bufferSize int
	)

	logPath = getStringOrDefault(config.Config.GetString("zaplog.logpath"), "./logs/")
	bufferSize = getIntOrDefault(config.Config.GetInt("zaplog.buffersize"), 4096)
	logLevel = getStringOrDefault(config.Config.GetString("zaplog.level"), "debug")
	lifeTime = getIntOrDefault(config.Config.GetInt("zaplog.loglifetime"), 7)
	logFile = getStringOrDefault(config.Config.GetString("zaplog.logfile"), "./data.log")
	writeSyncer := getLogWriter(logPath, lifeTime, logFile)
	//配置zap日志库的编码器
	encoder := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stack",
		EncodeTime:     zapcore.RFC3339NanoTimeEncoder,
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.FullCallerEncoder,
	}

	ws := &zapcore.BufferedWriteSyncer{
		WS:   zapcore.AddSync(writeSyncer),
		Size: bufferSize * 1024, // 512 kB
	}
	core = zapcore.NewCore(
		zapcore.NewJSONEncoder(encoder), // 编码器配置
		//zapcore.NewMultiWriteSyncer(zapcore.AddSync(os.Stdout), zapcore.AddSync(writeSyncer)), // 打印到控制台和文件
		ws,
		zap.NewAtomicLevelAt(getLogLevel(logLevel)), //日志级别
	)
	// 设置初始化字段,如：添加一个服务器名称
	HostName, _ = os.Hostname()
	filed := zap.Fields(zap.String("host_ip", resolveHostIP()), zap.String("host_name", HostName))
	zapLogger := zap.New(core,
		zap.AddStacktrace(zap.NewAtomicLevelAt(zapcore.ErrorLevel)),
		zap.AddCaller(),
		zap.AddCallerSkip(1),
		zap.Development(),
		filed,
	)

	logSugar := zapLogger.Sugar()
	Log = &ZapLogger{log: logSugar, Sync: zapLogger.Sync}
}

// 日志自动切割，采用 lumberjack 实现的
func getLogWriter(logPath string, lifeTime int, logFileName string) zapcore.WriteSyncer {
	baseLogPath := path.Join(logPath, logFileName)
	logStorePath := baseLogPath + ".zaplog"
	if len(logPath) > 0 {
		if ok, _ := pathExists(logPath); !ok {
			if err := os.MkdirAll(logPath, os.ModePerm); err != nil {
				panic(fmt.Errorf("Create zaplog path failed: %s\n", err.Error()))
			}
		}
	}
	lumberJackLogger := &lumberjack.Logger{
		Filename:   logStorePath, //指定日志存储位置
		MaxSize:    1024,         //日志的最大大小（M）
		MaxBackups: 0,            //旧日志的最大保存数量
		MaxAge:     lifeTime,     //旧日志文件存储最大天数
		Compress:   true,         //是否执行压缩
	}
	return zapcore.AddSync(lumberJackLogger)
}

func getLogLevel(level string) zapcore.Level {
	switch level {
	case "debug":
		return zapcore.DebugLevel
	case "info":
		return zapcore.InfoLevel
	case "warn":
		return zapcore.WarnLevel
	case "error":
		return zapcore.ErrorLevel
	case "fatal":
		return zapcore.FatalLevel
	case "panic":
		return zapcore.PanicLevel
	default:
		return zapcore.InfoLevel
	}
}

func getFields(ctx context.Context) []interface{} {
	var args []interface{}
	if ctx == nil {
		return args
	}
	if value := ctx.Value("call_id"); value != nil {
		args = append(args, zap.Any("call_id", value))
	}
	if value := ctx.Value("costTime"); value != nil {
		args = append(args, zap.Any("costTime", value))
	}
	return args
}

func Info(ctx context.Context, keyvals ...interface{}) {
	fields := getFields(ctx)
	if len(fields) == 0 {
		Log.log.Info(keyvals...)
	} else {
		newLog := Log.log.With(fields...)
		newLog.Info(keyvals...)
	}
}

func Infof(ctx context.Context, format string, keyvals ...interface{}) {
	fields := getFields(ctx)
	if len(fields) == 0 {
		Log.log.Infof(format, keyvals...)
	} else {
		newLog := Log.log.With(fields...)
		newLog.Infof(format, keyvals...)
	}
}

func Debugf(ctx context.Context, format string, keyvals ...interface{}) {
	fields := getFields(ctx)
	if len(fields) == 0 {
		Log.log.Debugf(format, keyvals...)
	} else {
		newLog := Log.log.With(fields...)
		newLog.Debugf(format, keyvals...)
	}
}

func Warn(ctx context.Context, keyvals ...interface{}) {
	fields := getFields(ctx)
	if len(fields) == 0 {
		Log.log.Warn(keyvals...)
	} else {
		newLog := Log.log.With(fields...)
		newLog.Warn(keyvals...)
	}
}

func Warnf(ctx context.Context, format string, keyvals ...interface{}) {
	fields := getFields(ctx)
	if len(fields) == 0 {
		Log.log.Warnf(format, keyvals...)
	} else {
		newLog := Log.log.With(fields...)
		newLog.Warnf(format, keyvals...)
	}
}

func Error(ctx context.Context, keyvals ...interface{}) {
	fields := getFields(ctx)
	if len(fields) == 0 {
		Log.log.Error(keyvals...)
	} else {
		newLog := Log.log.With(fields...)
		newLog.Error(keyvals...)
	}
}

func Errorf(ctx context.Context, format string, keyvals ...interface{}) {
	fields := getFields(ctx)
	if len(fields) == 0 {
		Log.log.Errorf(format, keyvals...)
	} else {
		newLog := Log.log.With(fields...)
		newLog.Errorf(format, keyvals...)
	}
}

// ResolveHostIp : a function to resolve localhost ip
func resolveHostIP() string {
	netInterfaceAddresses, err := net.InterfaceAddrs()
	if err != nil {
		return ""
	}

	for _, netInterfaceAddress := range netInterfaceAddresses {
		networkIp, ok := netInterfaceAddress.(*net.IPNet)
		if ok && !networkIp.IP.IsLoopback() && networkIp.IP.To4() != nil {
			ip := networkIp.IP.String()
			return ip
		}
	}
	return ""
}

// pathExists check if file or directory exists
func pathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		// does exist
		return true, nil
	} else if os.IsNotExist(err) {
		// does not exist
		return false, nil
	} else {
		// stat error
		return false, err
	}
}

// 获取字符串，空值返回默认值
func getStringOrDefault(s string, def string) string {
	if s == "" {
		return def
	} else {
		return s
	}
}

// 获取int，0值返回默认值
func getIntOrDefault(i int, def int) int {
	if i == 0 {
		return def
	} else {
		return i
	}
}
