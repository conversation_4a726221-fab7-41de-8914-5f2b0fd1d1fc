调用耗时
sum(rate(tts_service_request_duration_count[1m])) by (tts_type)
调用次数统计 - 总数
sum(increase(tts_service_request_duration_count[1m])) by (tts_type)
调用次数统计 - 成功
sum(increase(tts_service_request_duration_count{tts_response="success"}[1m])) by (tts_type)
调用次数统计 - 失败
sum(increase(tts_service_request_duration_count{tts_response="error"}[1m])) by (tts_type)
调用次数统计 - 实时tts成功
sum(increase(tts_service_request_duration_count{tts_response="realtime_cache_success"}[1m])) by (tts_type)
调用次数统计 - 实时缓存tts成功
sum(increase(tts_service_request_duration_count{tts_response="realtime_success"}[1m])) by (tts_type)
调用次数统计 - 实时失败
sum(increase(tts_service_request_duration_count{tts_response="realtime_error"}[1m])) by (tts_type)


调用次数统计 - 总数
sum(increase(tts_service_request_duration_count[1m]))
调用次数统计 - 成功
sum(increase(tts_service_request_duration_count{tts_response="success"}[1m]))
调用次数统计 - 失败
sum(increase(tts_service_request_duration_count{tts_response="error"}[1m]))
调用次数统计 - 实时tts成功
sum(increase(tts_service_request_duration_count{tts_response="realtime_cache_success"}[1m]))
调用次数统计 - 实时缓存tts成功
sum(increase(tts_service_request_duration_count{tts_response="realtime_success"}[1m]))
调用次数统计 - 实时失败
sum(increase(tts_service_request_duration_count{tts_response="realtime_error"}[1m]))

调用次数统计 - 机器人维度
sum(increase(tts_service_request_duration_count[1m])) by (robot_name)


sum(increase(tts_service_request_retry_success[1m]))
