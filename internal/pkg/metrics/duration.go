package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"strconv"
	"time"
)

const (
	ResponseSuccess = "success"
	ResponseError   = "error"

	ResponseV3Success = "success_v3"
	ResponseV3Error   = "error_v3"
	ResponseV3Cache   = "cache_v3"

	DefaultVec = "None"
)

var (
	RequestDuration = prometheus.NewSummaryVec(prometheus.SummaryOpts{
		Namespace:  "tts_service",
		Name:       "request_duration",
		Help:       "Summary of consume http request",
		Objectives: map[float64]float64{0.5: 0.05, 0.9: 0.01, 0.95: 0.005, 0.99: 0.001},
	}, []string{"robot_name", "tts_type", "tts_locale", "tts_speaker", "code", "company", "server_name", "stream", "response"})

	RequestBucket = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Namespace: "tts_service",
		Name:      "request_bucket",
		Buckets:   []float64{100, 500, 1000, 10000},
	}, []string{"robot_name", "tts_type", "tts_locale", "tts_speaker", "code", "company", "server_name", "stream", "response"})

	// RealtimeRtfDuration rtf 监控
	RealtimeRtfDuration = prometheus.NewSummaryVec(prometheus.SummaryOpts{
		Namespace:  "tts_service",
		Name:       "request_rtf_duration",
		Objectives: map[float64]float64{0.5: 0.05, 0.9: 0.01, 0.95: 0.005, 0.99: 0.001},
	}, []string{"robot_name", "tts_type", "tts_locale", "tts_speaker", "stream", "company", "server_name"})

	RealtimeRtfBucket = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Namespace: "tts_service",
		Name:      "request_rtf_bucket",
		Buckets:   []float64{0.6, 0.7, 0.8, 0.9, 1, 2},
	}, []string{"robot_name", "tts_type", "tts_locale", "tts_speaker", "stream", "company", "server_name"})

	// RealtimeFirstFrameDuration 第一帧延迟
	RealtimeFirstFrameDuration = prometheus.NewSummaryVec(prometheus.SummaryOpts{
		Namespace:  "tts_service",
		Name:       "request_first_frame_duration",
		Objectives: map[float64]float64{0.5: 0.05, 0.9: 0.01, 0.95: 0.005, 0.99: 0.001},
	}, []string{"robot_name", "tts_type", "tts_locale", "tts_speaker", "stream", "company", "server_name"})

	RealtimeFirstFrameBucket = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Namespace: "tts_service",
		Name:      "request_first_frame_bucket",
		Buckets:   []float64{200, 300, 500, 800, 1000, 1500, 2000, 3000},
	}, []string{"robot_name", "tts_type", "tts_locale", "tts_speaker", "stream", "company", "server_name"})

	// RealtimeEveryFrameDuration 每一帧延迟
	RealtimeEveryFrameDuration = prometheus.NewSummaryVec(prometheus.SummaryOpts{
		Namespace:  "tts_service",
		Name:       "request_every_frame_duration",
		Objectives: map[float64]float64{0.5: 0.05, 0.9: 0.01, 0.95: 0.005, 0.99: 0.001},
	}, []string{"robot_name", "tts_type", "tts_locale", "tts_speaker", "stream", "company", "server_name"})

	RealtimeEveryFrameBucket = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Namespace: "tts_service",
		Name:      "request_every_frame_bucket",
		Buckets:   []float64{200, 300, 500, 800, 1000, 1500, 2000, 3000},
	}, []string{"robot_name", "tts_type", "tts_locale", "tts_speaker", "stream", "company", "server_name"})

	// RealtimeDelayFrameDuration tts延迟长度
	RealtimeDelayFrameDuration = prometheus.NewSummaryVec(prometheus.SummaryOpts{
		Namespace:  "tts_service",
		Name:       "request_delay_frame_duration",
		Objectives: map[float64]float64{0.5: 0.05, 0.9: 0.01, 0.95: 0.005, 0.99: 0.001},
	}, []string{"robot_name", "tts_type", "tts_locale", "tts_speaker", "stream", "company", "server_name"})

	RealtimeDelayFrameBucket = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Namespace: "tts_service",
		Name:      "request_delay_frame_bucket",
		Buckets:   []float64{100, 200, 400, 800, 10000},
	}, []string{"robot_name", "tts_type", "tts_locale", "tts_speaker", "stream", "company", "server_name"})

	RealtimeGauge = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Namespace: "tts_service",
		Name:      "request_gauge",
	}, []string{"robot_name", "tts_type", "tts_locale", "tts_speaker", "stream", "company", "server_name"})

	RequestRetrySuccess = prometheus.NewCounter(prometheus.CounterOpts{
		Namespace: "tts_service",
		Name:      "request_retry_success",
	})

	RequestAlarmCallCounter = prometheus.NewCounterVec(prometheus.CounterOpts{
		Namespace: "tts_service",
		Name:      "alarm_call_counter",
	}, []string{"tts_type", "server_name", "detail", "error_code"})
)

func withDefault(value string) string {
	if value == "" {
		return DefaultVec
	}

	return value
}

func RequestObserve(robotName, ttsType, locale, speaker, company, serverName, response string, code int, stream bool, startTime time.Time) {
	labels := prometheus.Labels{
		"robot_name":  withDefault(robotName),
		"tts_type":    withDefault(ttsType),
		"tts_locale":  withDefault(locale),
		"tts_speaker": withDefault(speaker),
		"code":        strconv.Itoa(code),
		"company":     withDefault(company),
		"server_name": withDefault(serverName),
		"stream":      strconv.FormatBool(stream),
		"response":    response,
	}
	diff := float64(time.Now().Sub(startTime).Milliseconds())

	RequestDuration.With(labels).Observe(diff)
	RequestBucket.With(labels).Observe(diff)
}

func DurationObserve(summary *prometheus.SummaryVec, bucket *prometheus.HistogramVec, robotName, ttsType, locale, speaker, company, serverName string, stream bool, duration float64) {
	labels := prometheus.Labels{
		"robot_name":  withDefault(robotName),
		"tts_type":    withDefault(ttsType),
		"tts_locale":  withDefault(locale),
		"tts_speaker": withDefault(speaker),
		"server_name": withDefault(serverName),
		"company":     company,
		"stream":      strconv.FormatBool(stream),
	}
	summary.With(labels).Observe(duration)
	bucket.With(labels).Observe(duration)
}
