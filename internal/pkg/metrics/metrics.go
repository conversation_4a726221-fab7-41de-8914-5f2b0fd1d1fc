package metrics

import (
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

func PrometheusHandler() gin.HandlerFunc {
	h := promhttp.Handler()

	return func(c *gin.Context) {
		h.ServeHT<PERSON>(c.Writer, c.Request)
	}
}

func init() {
	prometheus.MustRegister(RequestDuration)
	prometheus.MustRegister(RequestBucket)
	prometheus.MustRegister(RealtimeRtfDuration)
	prometheus.MustRegister(RealtimeRtfBucket)
	prometheus.MustRegister(RealtimeFirstFrameDuration)
	prometheus.MustRegister(RealtimeFirstFrameBucket)
	prometheus.MustRegister(RealtimeEveryFrameDuration)
	prometheus.MustRegister(RealtimeEveryFrameBucket)
	prometheus.MustRegister(RealtimeDelayFrameDuration)
	prometheus.MustRegister(RealtimeDelayFrameBucket)
	prometheus.MustRegister(RequestRetrySuccess)
	prometheus.MustRegister(RealtimeGauge)
	prometheus.MustRegister(RequestAlarmCallCounter)
}
