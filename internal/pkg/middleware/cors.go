package middleware

import "github.com/gin-gonic/gin"

func CorsMiddleware(q *gin.Context) {
	q.Writer.Header().Set("Access-Control-Allow-Origin", q.<PERSON>Header("Origin"))
	q.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
	q.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With, X-Token")
	q.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT")

	if q.Request.Method == "OPTIONS" {
		q.AbortWithStatus(200)
		return
	}
	q.Next()
}
