package controller

import (
	"archive/zip"
	"encoding/csv"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"io"
	"strings"
	"time"
	"tts-service/internal/pkg/cache"
	"tts-service/internal/pkg/code"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/wav"
	"tts-service/internal/tts_service/grpc_server/realtime_tts"
	"tts-service/internal/tts_service/models"
	"tts-service/pkg"
)

var allowdModels = map[string]struct{}{
	"google":     {},
	"microsoft":  {},
	"airudder":   {},
	"elevenlabs": {},
}

func TTSCacheAdd(ctx *gin.Context) {
	var audioParams models.CacheAudioParams
	if err := ctx.Bind(&audioParams); err != nil {
		Error(ctx, nil, code.ErrorParams, err.Error())
		return
	}

	f, _ := audioParams.AudioFile.Open()
	defer f.Close()

	content, err := io.ReadAll(f)
	if err != nil {
		Error(ctx, nil, code.ErrorParams, errors.Wrap(err, "read audio file error").Error())
		return
	}

	wavFileContent, err := pkg.ConvertMp3ToWav(ctx, content)
	if err != nil {
		Error(ctx, nil, code.ErrorInternal, fmt.Sprintf("convert mp3 to wav error: %s", err.Error()))
		return
	}

	wavAudio, err := wav.GetWavAudio(wavFileContent)
	if err != nil {
		Error(ctx, nil, code.ErrorInternal, fmt.Sprintf("wav audio encode error: %s", err.Error()))
		return
	}

	v2CacheKey := fmt.Sprintf(
		"%s_%s_%s_%s",
		strings.ToLower(audioParams.GetTTSType()),
		strings.ToLower(audioParams.Locale),
		strings.ToLower(audioParams.Speaker),
		audioParams.Text)

	cache.FileClient.SetV2(v2CacheKey, &realtime_tts.AudioResponse{
		Content:       wavAudio.Bytes(),
		Code:          code.ServerOK,
		GenerateTime:  time.Now().UnixMilli(),
		GeneratedTime: time.Now().UnixMilli(),
		Duration:      float32(wavAudio.Duration() / 1e3),
		IsEnd:         true,
	})

	Success(ctx, nil)
	return
}

func TTSCacheBatchAdd(ctx *gin.Context) {
	var request models.CacheBatchAudioParams
	if err := ctx.Bind(&request); err != nil {
		Error(ctx, nil, code.ErrorParams, err.Error())
		return
	}

	audioFiles, err := request.AudioFile.Open()
	if err != nil {
		Error(ctx, nil, code.ErrorParams, fmt.Sprintf("audio file open error: %s", err.Error()))
		return
	}

	defer audioFiles.Close()

	reader, err := zip.NewReader(audioFiles, request.AudioFile.Size)
	if err != nil {
		Error(ctx, nil, code.ErrorParams, fmt.Sprintf("zip reader error: %s", err.Error()))
		return
	}

	audioNameMaps := make(map[string]int, len(reader.File))
	for index, f := range reader.File {
		fNames := strings.Split(f.Name, "/")
		fileName := fNames[len(fNames)-1]

		audioNameMaps[fileName] = index
	}

	csvFile, err := request.CsvFile.Open()
	if err != nil {
		Error(ctx, nil, code.ErrorParams, fmt.Sprintf("csv file open error: %s", err.Error()))
		return
	}

	defer csvFile.Close()

	r := csv.NewReader(csvFile)
	records, err := r.ReadAll()
	if err != nil {
		Error(ctx, nil, code.ErrorParams, fmt.Sprintf("csv file encode error: %s", err.Error()))
		return
	}

	errMessage := make([]string, 0)
	audios := make([]models.CacheAudio, 0, len(records))
	for index, record := range records {
		if index == 0 {
			continue
		}

		if len(record) < 5 {
			errMessage = append(errMessage, fmt.Sprintf("row %d: There are not enough fields to fill in.", index+1))
			continue
		}

		if _, ok := allowdModels[strings.ToLower(record[1])]; !ok {
			errMessage = append(errMessage, fmt.Sprintf("row %d: model %s is not allowed.", index+1, record[1]))
			continue
		}

		audio := models.CacheAudio{
			TTSType: record[1],
			Locale:  record[2],
			Speaker: record[3],
			Text:    record[4],
		}

		if audioIndex, ok := audioNameMaps[record[0]]; ok {
			audio.Audio = reader.File[audioIndex]
		} else {
			errMessage = append(errMessage, fmt.Sprintf("row %d: audio %s does not exists", index+1, record[0]))
			continue
		}

		audios = append(audios, audio)
	}

	if len(errMessage) > 0 {
		Error(ctx, nil, code.ErrorParams, strings.Join(errMessage, "\n"))
		return
	}

	findAll := false
	for _, region := range request.GetRegions() {
		if region == "all" {
			findAll = true
			break
		}
	}

	var addresses []string
	if findAll {
		for _, value := range config.CacheV2Address {
			addresses = append(addresses, value)
		}
	} else {
		for _, region := range request.GetRegions() {
			if address, ok := config.CacheV2Address[region]; ok {
				addresses = append(addresses, address)
			}
		}
	}

	client := GetCacheAddClient()
	for _, audio := range audios {
		audioFile, err := audio.Audio.Open()
		if err != nil {
			Error(ctx, nil, code.ErrorParams, fmt.Sprintf("audio file open error: %s", err.Error()))
			return
		}

		audioContent, err := io.ReadAll(audioFile)
		if err != nil {
			Error(ctx, nil, code.ErrorParams, fmt.Sprintf("audio file read error: %s", err.Error()))
			return
		}
		for _, address := range addresses {
			if err := client.AddCache(address, audio.TTSType, audio.Locale, audio.Speaker, audio.Text, audioContent); err != nil {
				Error(ctx, nil, code.ErrorParams, err.Error())
				return
			}
		}

	}

	Success(ctx, nil)
}
