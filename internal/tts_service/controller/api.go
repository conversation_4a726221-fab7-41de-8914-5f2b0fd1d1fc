package controller

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"tts-service/internal/pkg/alarm"
	"tts-service/internal/pkg/code"
	"tts-service/internal/tts_service/models"
)

func Error(ctx *gin.Context, request *models.TTSRequest, errorCode int, message string) {
	// 报警服务
	if request != nil {
		go alarm.P3(ctx, request, errorCode, message, !code.CheckCodeNeedRepeated(errorCode))
	}

	ctx.JSON(http.StatusOK, models.TTSResponse{
		Status:       message,
		Code:         errorCode,
		NeedRepeated: code.CheckCodeNeedRepeated(errorCode),
	})
}

func Success(ctx *gin.Context, data interface{}) {
	ctx.JSON(http.StatusOK, models.TTSResponse{
		Data:   data,
		Status: "",
		Code:   code.ServerOK,
	})
}
