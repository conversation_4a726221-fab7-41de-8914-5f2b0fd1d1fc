package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"
	"net/http"
	"sync"
	"time"
	"tts-service/internal/pkg/code"
	"tts-service/internal/tts_service/models"
)

const (
	CacheClientTimeOut = 10 * time.Second
)

var (
	cacheClient *CacheAddClient
	cacheOnce   sync.Once
)

type CacheAddClient struct {
	http *resty.Client
}

func GetCacheAddClient() *CacheAddClient {
	cacheOnce.Do(func() {
		cacheClient = &CacheAddClient{
			http: resty.New().SetTimeout(CacheClientTimeOut),
		}
	})

	return cacheClient
}

func (client *CacheAddClient) AddCache(address, ttsType, locale, speaker, text string, audioFile []byte) error {
	resp, err := client.http.R().
		SetFileReader("audio_file", "audio_file.mp3", bytes.NewReader(audioFile)).
		SetFormData(map[string]string{
			"tts_type": ttsType,
			"locale":   locale,
			"speaker":  speaker,
			"text":     text,
		}).
		Post(address)

	if err != nil {
		return errors.Wrapf(err, "post address error: %s", address)
	}

	if resp.StatusCode() != http.StatusOK {
		return errors.Errorf("post address error: %s", address)
	}

	var response models.TTSResponse
	if err := json.Unmarshal(resp.Body(), &response); err != nil {
		return errors.Wrapf(err, "post address error: %s", address)
	}

	if response.Code != code.ServerOK {
		return errors.New(fmt.Sprintf("post address error: %s, status=%s", address, response.Status))
	}

	return nil
}
