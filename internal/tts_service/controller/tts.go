package controller

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"net/http"
	"os"
	"strconv"
	"time"
	"tts-service/internal/pkg/cache"
	"tts-service/internal/pkg/code"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/constants"
	"tts-service/internal/pkg/log"
	"tts-service/internal/pkg/metrics"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service"
)

func getRequest(ctx *gin.Context) (*models.TTSRequest, error) {
	var request models.TTSRequest
	if err := ctx.Bind(&request); err != nil {
		return nil, errors.Wrap(err, "request params error")
	}

	log.Infof(ctx, "TTS request params: %+v", request)
	ctx.Set("call_id", request.CallId)
	return &request, nil
}

func TTSController(ctx *gin.Context) {
	startTime := time.Now()
	request, err := getRequest(ctx)
	if err != nil {
		log.Error(ctx, err.Error())
		Error(ctx, request, code.ErrorParams, err.Error())
		return
	}

	err, provider := request.GetProvider()
	if err != nil {
		log.Error(ctx, err.Error())
		response := models.NewErrorResponse(code.ErrorParams, err.Error())
		Error(ctx, request, response.Code, response.Status)
		return
	}

	// 对于 airudder__v1 的服务，转发到老版本 tts-service 服务
	if provider == constants.AirudderV1Provider {
		if number, err := strconv.ParseFloat(request.Text, 64); err == nil && number < 0 {
			Error(ctx, request, code.ErrorParams, fmt.Sprintf("The Airudder-V1 engine does not support negative numbers：%f", number))
			return
		}

		ctx.Redirect(http.StatusFound, fmt.Sprintf(
			"%s/tts?%s", config.Config.GetString("tts_v1.url"), request.ToAirudderV1GetUrl()))
		metrics.RequestObserve(request.RobotName, provider, request.Locale,
			request.Speaker, request.Company, request.ServerName,
			metrics.ResponseSuccess, code.ServerOK, false, startTime)
	} else {
		response, wav := service.GenerateTTS(ctx, provider, request)
		if response != nil {
			metrics.RequestObserve(request.RobotName, provider, request.Locale,
				request.Speaker, request.Company, request.ServerName,
				metrics.ResponseError, response.Code, false, startTime)
			Error(ctx, request, response.Code, response.Status)
			return
		}

		metrics.RequestObserve(request.RobotName, provider, request.Locale,
			request.Speaker, request.Company, request.ServerName,
			metrics.ResponseSuccess, code.ServerOK, false, startTime)
		Success(ctx, wav.Content)
	}
}

func TTSTestController(ctx *gin.Context) {
	request, err := getRequest(ctx)
	if err != nil {
		log.Error(ctx, err.Error())
		Error(ctx, request, code.ErrorParams, err.Error())
		return
	}

	err, provider := request.GetProvider()
	if err != nil {
		log.Error(ctx, err.Error())
		response := models.NewErrorResponse(code.ErrorParams, err.Error())
		Error(ctx, request, response.Code, response.Status)
		return
	}

	// 对于 airudder__v1 的服务，转发到老版本 tts-service 服务
	if provider == constants.AirudderV1Provider {
		ctx.Redirect(http.StatusFound, fmt.Sprintf("%s%s", config.Config.GetString("tts_v1.url"), ctx.Request.RequestURI))
	} else {
		response, wav := service.GenerateTTS(ctx, provider, request)
		if response != nil {
			Error(ctx, request, response.Code, response.Status)
			return
		}

		// 写入文件
		filename, err := service.WriteToFile(ctx, request, wav.Content)
		if err != nil {
			Error(ctx, request, code.ErrorInternal, err.Error())
			return
		}

		Success(ctx, fmt.Sprintf("%s/%s", config.Config.GetString("localhost"), filename))
	}
}

func TTSCacheDelete(ctx *gin.Context) {
	var request struct {
		Filenames []string `json:"filenames" form:"filenames"`
	}

	if err := ctx.BindJSON(&request); err != nil {
		log.Error(ctx, err.Error())
		Error(ctx, nil, -1, err.Error())
		return
	}

	for _, filename := range request.Filenames {
		if err := cache.FileClient.Delete(filename); err != nil {
			if os.IsNotExist(err) {
				continue
			}

			log.Error(ctx, err.Error())
			Error(ctx, nil, -1, err.Error())
			return
		}
	}

	Success(ctx, "ok")
}

func TTSCacheReload(ctx *gin.Context) {
	config.ReloadCacheMap()

	Success(ctx, "ok")
}
