package deepgram

import (
	"context"
	"os"
	"testing"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
)

func TestStreamAudio(t *testing.T) {
	log.InitLogger()
	config.DeepgramAuthKey = "Token ****************************************"

	request := &models.TTSRequest{
		Speaker: "aura-asteria-en",
	}

	ttsParams := &params.TtsParams{
		Input: &params.InputParams{
			SSML: "Hey guys, if you're ever in the Big Apple, come check out my pad in the heart of Manhattan! My address is 123 Main Street, New York, NY. It's just a short walk from Times Square and has all the best restaurants and shops nearby.",
		},
	}

	file, _ := os.OpenFile("/Users/<USER>/Desktop/test4.wav", os.O_WRONLY|os.O_CREATE, 0666)
	defer file.Close()
	if err := StreamAdapter(context.Background(), request, ttsParams, file); err != nil {
		t.Error(err)
	}

	_ = file.Sync()
}
