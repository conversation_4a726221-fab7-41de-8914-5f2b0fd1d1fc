package deepgram

import (
	"context"
	"io"
	"tts-service/internal/pkg/code"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
)

type Engine struct {
	code    int
	err     error
	content []byte
}

func (engine *Engine) GetCode() int {
	return engine.code
}

func (engine *Engine) GetStatus() string {
	return engine.err.Error()
}

func (engine *Engine) GetAudio() *models.Wav {
	return &models.Wav{Content: engine.content}
}

func (engine *Engine) GenerateAudio(ctx context.Context, request *models.TTSRequest) bool {
	ttsParams, err := params.NewTTSParams(request)
	if err != nil {
		engine.code = code.ErrorMissingParams
		engine.err = err
		return false
	}

	ttsParams.Container = "wav"
	reader, err := GetStreamResponse(ctx, request, ttsParams)
	if err != nil {
		engine.code = code.ErrorRequestInternal
		engine.err = err
		return false
	}

	audioContent, err := io.ReadAll(reader)
	if err != nil {
		engine.code = code.ErrorRequestInternal
		engine.err = err
		return false
	}

	engine.content = audioContent
	return true
}
