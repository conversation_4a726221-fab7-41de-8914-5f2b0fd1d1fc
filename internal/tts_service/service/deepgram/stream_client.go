package deepgram

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"io"
	"net/http"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/constants"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
)

func GetStreamResponse(ctx context.Context, request *models.TTSRequest, params *params.TtsParams) (io.ReadCloser, error) {
	provider := constants.DeepgramProvider
	container := params.Container
	if container == "" {
		container = "none"
	}
	url := fmt.Sprintf("https://api.deepgram.com/v1/speak?model=%s&encoding=linear16&sample_rate=8000&container=%s", params.ConvertSpeaker(request.Speaker, provider), container)

	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": config.DeepgramAuthKey,
	}

	data := map[string]interface{}{
		"text": params.GetTtsSSML(request, provider),
	}

	log.Infof(ctx, "[deepgram] start http request: locale=%s, speaker=%s, text=%s", request.Locale, request.Speaker, data["text"])
	jsonData, _ := json.Marshal(data)
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "connect to openai failed")
	}

	if resp.StatusCode != 200 {
		return nil, errors.Errorf("openai response status code is %d", resp.StatusCode)
	}

	return resp.Body, nil
}

func StreamAdapter(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, inPipe io.WriteCloser) error {
	content, err := GetStreamResponse(ctx, request, ttsParams)

	if err != nil {
		log.Infof(ctx, "[elevenlabs] get response failed: %s", err.Error())
		return err
	}
	defer content.Close()

	buffer := make([]byte, 4096)
	size := 0
	for {
		n, err := content.Read(buffer)
		if err == io.EOF {
			break
		}

		if err != nil {
			return err
		}

		size += n
		_, _ = inPipe.Write(buffer[:n])
	}

	return nil
}
