package microsoft

import (
	"context"
	"github.com/pkg/errors"
	"tts-service/internal/pkg/code"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
)

type Engine struct {
	code    int
	err     error
	content []byte
}

func (engine *Engine) GetCode() int {
	return engine.code
}

func (engine *Engine) GetStatus() string {
	return engine.err.Error()
}

func (engine *Engine) GetAudio() *models.Wav {
	return &models.Wav{Content: engine.content}
}

func (engine *Engine) GenerateAudio(ctx context.Context, request *models.TTSRequest) bool {
	if request.Locale == "" || request.Speaker == "" || request.Text == "" {
		engine.code = code.ErrorMissingParams
		engine.err = errors.New("microsoft v1, param miss")
		return false
	}

	ttsParams, err := params.NewTTSParams(request)
	if err != nil {
		engine.code = code.ErrorMissingParams
		engine.err = err
		return false
	}
	client := GetClient(request.AccountType)
	var response *params.ClientResponse
	response, engine.code, engine.err = client.GetAudio(ctx, request, ttsParams, 0)

	if engine.code == code.ServerOK {
		engine.content = response.AudioContent
		return true
	}

	return false
}
