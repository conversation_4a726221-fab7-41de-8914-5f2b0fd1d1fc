package microsoft

import (
	"context"
	"encoding/csv"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"
	"net/http"
	"net/url"
	"os"
	"sync"
	"time"
	"tts-service/internal/pkg/code"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
	"tts-service/pkg/pck2wav"
)

type ClientAccount struct {
	region   string
	endpoint string
	resource string
	key      string

	http *resty.Client
}

type ClientManager struct {
	index   map[string]int
	clients map[string][]*ClientAccount

	sync.Mutex
}

const (
	defaultAccountType = "common"
	clientTimeOut      = 40
)

var (
	clientManager *ClientManager
)

func (clinet *ClientAccount) checkErrorCode(resp *resty.Response) int {
	switch resp.StatusCode() {
	case http.StatusBadRequest:
		return code.ErrorSpeak
	case http.StatusTooManyRequests:
		return code.ErrorQuota
	case http.StatusOK:
		return code.ServerOK
	}

	return code.ErrorInternal
}

func (client *ClientAccount) GetAudio(_ context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, _ int) (*params.ClientResponse, int, error) {
	speechUrl := fmt.Sprintf("https://%s.tts.speech.microsoft.com/cognitiveservices/v1", client.region)
	resp, err := client.http.R().
		SetHeader("X-Microsoft-OutputFormat", "raw-8khz-16bit-mono-pcm").
		SetHeader("Content-Type", "application/ssml+xml").
		SetHeader("Ocp-Apim-Subscription-Key", client.key).
		SetBody(ttsParams.ToMicrosoftSSML(request)).
		Post(speechUrl)

	if err != nil {
		if err, ok := err.(*url.Error); ok && err.Timeout() {
			return nil, code.ErrorTimeout, err
		}
		return nil, code.ErrorInternal, err
	}

	errorCode := client.checkErrorCode(resp)
	if errorCode == code.ServerOK {
		content, err := pck2wav.Pcm2Wav(resp.Body(), request.GetDefaultSampleRate())
		if err != nil {
			return nil, code.ErrorInternal, errors.Wrap(err, "convert pcm to wav error")
		}

		return &params.ClientResponse{
			AudioContent: content,
			Code:         code.ServerOK,
		}, code.ServerOK, nil
	}

	return nil, errorCode, errors.New(fmt.Sprintf("Reason=%d, detail=%s", resp.StatusCode(), string(resp.Body())))
}

func (clientManager *ClientManager) initAccountTypeClients(accountType string, filename string) error {
	file, err := os.OpenFile(filename, os.O_RDONLY, 0600)
	if err != nil {
		return errors.Wrap(err, "read microsoft csv file error")
	}

	defer file.Close()
	reader := csv.NewReader(file)
	accounts, err := reader.ReadAll()
	if err != nil {
		return errors.Wrap(err, "read microsoft clientAccount file error")
	}

	clientManager.clients[accountType] = make([]*ClientAccount, 0)
	clientManager.index[accountType] = 0
	for index := 1; index < len(accounts); index++ {
		clientManager.clients[accountType] = append(clientManager.clients[accountType], &ClientAccount{
			region:   accounts[index][0],
			endpoint: accounts[index][1],
			resource: accounts[index][2],
			key:      accounts[index][3],
			http:     resty.New().SetTimeout(clientTimeOut * time.Second),
		})
	}

	return nil
}

func (clientManager *ClientManager) getIndex(accountType string) int {
	clientManager.Lock()
	defer clientManager.Unlock()

	index := clientManager.index[accountType]
	clientManager.index[accountType] = (index + 1) % len(clientManager.clients[accountType])
	return index
}

func InitClients(ctx context.Context) error {
	clientManager = &ClientManager{
		index:   make(map[string]int, 0),
		clients: make(map[string][]*ClientAccount, 0),
	}

	err := clientManager.initAccountTypeClients("common", "accounts/microsoft/common.csv")
	if err == nil {
		err = clientManager.initAccountTypeClients("ai", "accounts/microsoft/ai.csv")
	}

	if err == nil {
		err = clientManager.initAccountTypeClients("aliyun-hk-realtime", "accounts/microsoft/aliyun-hk-realtime.csv")
	}

	if err == nil {
		err = clientManager.initAccountTypeClients("aliyun-va-realtime", "accounts/microsoft/aliyun-va-realtime.csv")
	}

	return err
}

func GetClient(accountType string) *ClientAccount {
	if _, ok := clientManager.clients[accountType]; !ok {
		accountType = defaultAccountType
	}

	index := clientManager.getIndex(accountType)
	account := clientManager.clients[accountType][index]

	return account
}
