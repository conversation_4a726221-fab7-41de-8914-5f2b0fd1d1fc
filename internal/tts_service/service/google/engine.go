package google

import (
	"context"
	"github.com/pkg/errors"
	texttospeechpb "google.golang.org/genproto/googleapis/cloud/texttospeech/v1"
	"strings"
	"tts-service/internal/pkg/code"
	"tts-service/internal/pkg/utils"
	"tts-service/internal/tts_service/models"
)

type Engine struct {
	code    int
	err     error
	content []byte
}

func (engine *Engine) checkErrorCode(err error) int {
	errMsg := err.Error()
	if strings.Contains(errMsg, "InvalidArgument") {
		return code.ErrorSpeak
	}
	if strings.Contains(errMsg, "OutOfRange") {
		return code.ErrorParams
	}
	if strings.Contains(errMsg, "ResourceExhausted") {
		return code.ErrorQuota
	}
	if strings.Contains(errMsg, "DeadlineExceeded") {
		return code.ErrorTimeout
	}

	return code.ErrorInternal
}

func (engine *Engine) GetCode() int {
	return engine.code
}

func (engine *Engine) GetStatus() string {
	return engine.err.Error()
}

func (engine *Engine) GetAudio() *models.Wav {
	return &models.Wav{Content: engine.content}
}

func (engine *Engine) GenerateAudio(ctx context.Context, request *models.TTSRequest) bool {
	if request.Locale == "" || request.Speaker == "" || request.Text == "" {
		engine.code = code.ErrorMissingParams
		engine.err = errors.New("google v1, param miss")
		return false
	}

	var input *texttospeechpb.SynthesisInput
	if utils.IsSSMLText(request.Text) {
		input = &texttospeechpb.SynthesisInput{
			InputSource: &texttospeechpb.SynthesisInput_Ssml{Ssml: request.Text},
		}
	} else {
		input = &texttospeechpb.SynthesisInput{
			InputSource: &texttospeechpb.SynthesisInput_Text{Text: request.Text},
		}
	}

	speakingRate := request.SpeakingRate
	if speakingRate == 0 {
		speakingRate = 1.0
	}

	req := texttospeechpb.SynthesizeSpeechRequest{
		Input: input,
		Voice: &texttospeechpb.VoiceSelectionParams{
			LanguageCode: request.Locale,
			Name:         request.Speaker,
		},
		AudioConfig: &texttospeechpb.AudioConfig{
			AudioEncoding: texttospeechpb.AudioEncoding_LINEAR16,
			SpeakingRate:  speakingRate,
			VolumeGainDb:  utils.CheckVolumeGainDb(request.VolumeGainDb),
		},
	}

	resp, err := GetClient(request.AccountType).SynthesizeSpeech(ctx, &req)

	if err != nil {
		engine.code = engine.checkErrorCode(err)
		engine.err = errors.Wrap(err, "google request error")
		return false
	}

	engine.content = resp.AudioContent
	return true
}
