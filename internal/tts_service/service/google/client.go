package google

import (
	texttospeech "cloud.google.com/go/texttospeech/apiv1"
	"context"
	"github.com/pkg/errors"
	"google.golang.org/api/option"
	"io/fs"
	"path/filepath"
	"sync"
)

type clientManager struct {
	index   map[string]int
	clients map[string][]*texttospeech.Client

	sync.Mutex
}

const (
	defaultAccountType = "common"
)

var (
	client *clientManager
)

func (client *clientManager) initAccountTypeClients(ctx context.Context, accountType string, dirName string) error {
	client.clients[accountType] = make([]*texttospeech.Client, 0)
	client.index[accountType] = 0
	return filepath.Walk(dirName, func(path string, info fs.FileInfo, err error) error {
		if err != nil {
			return errors.Wrap(err, "read google account error")
		}

		if !info.IsDir() {
			googleClient, err := texttospeech.NewClient(ctx, option.WithCredentialsFile(path))
			if err != nil {
				return errors.Wrap(err, "init google client with credentials file error")
			}

			client.clients[accountType] = append(client.clients[accountType], googleClient)
		}

		return nil
	})
}

func (client *clientManager) getIndex(accountType string) int {
	client.Lock()
	defer client.Unlock()

	index := client.index[accountType]
	client.index[accountType] = (index + 1) % len(client.clients[accountType])
	return index
}

func InitClients(ctx context.Context) error {
	client = &clientManager{
		index:   make(map[string]int, 0),
		clients: make(map[string][]*texttospeech.Client, 0),
	}

	err := client.initAccountTypeClients(ctx, "common", "accounts/google/common")
	if err == nil {
		err = client.initAccountTypeClients(ctx, "ivr", "accounts/google/ivr")
	}

	if err == nil {
		err = client.initAccountTypeClients(ctx, "livedemo", "accounts/google/livedemo")
	}

	if err == nil {
		err = client.initAccountTypeClients(ctx, "poc", "accounts/google/poc")
	}

	if err == nil {
		err = client.initAccountTypeClients(ctx, "ai", "accounts/google/ai")
	}

	return err
}

func GetClient(accountType string) *texttospeech.Client {
	if _, ok := client.clients[accountType]; !ok {
		accountType = defaultAccountType
	}

	index := client.getIndex(accountType)
	return client.clients[accountType][index]
}
