package convert

import (
	"context"
	"github.com/pkg/errors"
	"io"
	"time"
	"tts-service/internal/pkg/code"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
)

type ContentAdapter func(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams) (io.ReadCloser, error)

func ConvertAdapter(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, adapter ContentAdapter) <-chan *StreamAudio {
	startTime := time.Now()
	var err error
	var content io.ReadCloser

	responseChan := make(chan *StreamAudio, 100)
	go func() {
		defer close(responseChan)

		content, err = adapter(ctx, request, ttsParams)
		if err != nil {
			responseChan <- &StreamAudio{
				Err:       err,
				ErrorCode: code.ErrorInternal,
			}
			return
		}

		defer content.Close()
		buffer := make([]byte, ChunkSize)

		audioContent := make([]byte, 0, ChunkSize*10)
		for err == nil {
			n, readErr := content.Read(buffer)
			audioContent = append(audioContent, buffer[:n]...)
			if readErr == io.EOF {
				break
			}

			if readErr != nil {
				log.Infof(ctx, "read stream audio failed: %s", readErr.Error())
				err = errors.Wrap(err, "read stream audio failed")
				break
			}

			if len(audioContent) >= ChunkSize {
				responseChan <- &StreamAudio{
					Response: &params.ClientResponse{
						AudioContent: audioContent[:ChunkSize],
						Code:         code.ServerOK,
						Duration:     200 * 1000,
						DelayTime:    float64(time.Now().Sub(startTime).Milliseconds()),
					},
				}

				startTime = time.Now()
				audioContent = audioContent[ChunkSize:]
			}
		}

		if err != nil {
			log.Infof(ctx, "read stream audio failed: %s", err.Error())
			responseChan <- &StreamAudio{
				Err:       err,
				ErrorCode: code.ErrorInternal,
			}
			return
		}

		responseChan <- &StreamAudio{
			Response: &params.ClientResponse{
				AudioContent: audioContent,
				Code:         code.ServerOK,
				IsEnd:        true,
				Duration:     int64(len(audioContent)/MillSize) * 1000,
				DelayTime:    float64(time.Now().Sub(startTime).Milliseconds()),
			},
		}
	}()

	return responseChan
}
