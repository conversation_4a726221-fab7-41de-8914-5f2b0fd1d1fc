package convert

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"io"
	"os/exec"
	"time"
	"tts-service/internal/pkg/code"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
)

const (
	MillSize  = 16
	ChunkSize = MillSize * 200
)

type StreamAudio struct {
	Response  *params.ClientResponse
	Err       error
	ErrorCode int
}

type Adapter func(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, inPipe io.WriteCloser) error

func PcmConvertAdapter(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, adapter Adapter) <-chan *StreamAudio {
	var cmd *exec.Cmd
	if request.SpeakingRate == 0 || request.SpeakingRate == 1 {
		cmd = exec.Command("ffmpeg", "-i", "pipe:0", "-f", "s16le", "-ar", "8000", "-acodec", "pcm_s16le", "-")
	} else {
		cmd = exec.Command("ffmpeg", "-i", "pipe:0", "-filter:a", fmt.Sprintf("atempo=%f", request.SpeakingRate), "-f", "s16le", "-ar", "8000", "-acodec", "pcm_s16le", "-")
	}
	outPipe, _ := cmd.StdoutPipe()
	inPipe, _ := cmd.StdinPipe()

	_, responseChan := Convert(ctx, request, ttsParams, adapter, inPipe, outPipe)

	if startErr := cmd.Start(); startErr != nil {
		log.Infof(ctx, "start ffmpeg failed: %s", startErr.Error())
		responseChan <- &StreamAudio{
			Err:       startErr,
			ErrorCode: code.ErrorInternal,
		}
	}
	return responseChan
}

func Convert(
	ctx context.Context,
	request *models.TTSRequest,
	ttsParams *params.TtsParams,
	adapter Adapter,
	inPipe io.WriteCloser,
	outPipe io.ReadCloser,
) (error, chan *StreamAudio) {
	var err error
	startTime := time.Now()
	go func() {
		defer inPipe.Close()
		err = adapter(ctx, request, ttsParams, inPipe)
	}()

	responseChan := make(chan *StreamAudio, 100)
	go func() {
		defer close(responseChan)

		buffer := make([]byte, ChunkSize)

		audioContent := make([]byte, 0, ChunkSize*10)
		for err == nil {
			n, readErr := outPipe.Read(buffer)
			audioContent = append(audioContent, buffer[:n]...)
			if readErr == io.EOF && err == nil {
				break
			}

			if readErr != nil && readErr != io.EOF {
				log.Infof(ctx, "read stream audio failed: %s", readErr.Error())
				err = errors.Wrap(err, "read stream audio failed")
				break
			}

			if len(audioContent) >= ChunkSize {
				responseChan <- &StreamAudio{
					Response: &params.ClientResponse{
						AudioContent: audioContent[:ChunkSize],
						Code:         code.ServerOK,
						Duration:     200 * 1000,
						DelayTime:    float64(time.Now().Sub(startTime).Milliseconds()),
					},
				}

				startTime = time.Now()
				audioContent = audioContent[ChunkSize:]
			}
		}

		if err != nil {
			log.Infof(ctx, "read stream audio failed: %s", err.Error())
			responseChan <- &StreamAudio{
				Err:       err,
				ErrorCode: code.ErrorInternal,
			}
			return
		}

		responseChan <- &StreamAudio{
			Response: &params.ClientResponse{
				AudioContent: audioContent,
				Code:         code.ServerOK,
				IsEnd:        true,
				Duration:     int64(len(audioContent)/MillSize) * 1000,
				DelayTime:    float64(time.Now().Sub(startTime).Milliseconds()),
			},
		}
	}()
	return err, responseChan
}

func EmptyAdapter(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, adapter Adapter) <-chan *StreamAudio {
	outPipe, inPipe := io.Pipe()
	_, responseChan := Convert(ctx, request, ttsParams, adapter, inPipe, outPipe)
	return responseChan
}
