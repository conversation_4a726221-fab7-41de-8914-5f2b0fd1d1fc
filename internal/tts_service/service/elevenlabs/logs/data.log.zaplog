{"level":"INFO","time":"2025-04-08T16:44:03.025922+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/stream_client.go:182","msg":"[elevenlabs] start http request: locale=eleven_monolingual_v1, speaker=21m00Tcm4TlvDq8ikWAM, text=I am a Pest Control OpenAI assistant. Before I transfer you, model_id=en-PH,voice_settings=map[similarity_boost:0 speed:1 stability:0.21]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-04-08T17:00:33.681968+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/stream_client.go:182","msg":"[elevenlabs] start http request: locale=eleven_monolingual_v1, speaker=21m00Tcm4TlvDq8ikWAM, text=I am a Pest Control OpenAI assistant. Before I transfer you, model_id=en-PH,voice_settings=map[similarity_boost:0 speed:1 stability:0]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-04-08T17:02:17.894253+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/stream_client.go:182","msg":"[elevenlabs] start http request: locale=eleven_monolingual_v1, speaker=21m00Tcm4TlvDq8ikWAM, text=I am a Pest Control OpenAI assistant. Before I transfer you, model_id=en-PH,voice_settings=map[similarity_boost:0 speed:1 stability:0.21]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-04-08T17:10:15.941736+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/stream_client.go:182","msg":"[elevenlabs] start http request: locale=eleven_monolingual_v1, speaker=21m00Tcm4TlvDq8ikWAM, text=I am a Pest Control OpenAI assistant. Before I transfer you, model_id=en-PH,voice_settings=map[similarity_boost:0 stability:0.21]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-04-08T17:22:55.132606+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/stream_client.go:182","msg":"[elevenlabs] start http request: url=https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM/stream?optimize_streaming_latency=0&output_format=pcm_8000,locale=eleven_monolingual_v1, speaker=21m00Tcm4TlvDq8ikWAM, text=I am a Pest Control OpenAI assistant. Before I transfer you, model_id=en-PH,voice_settings=map[similarity_boost:0 speed:1 stability:0.21]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-04-08T17:44:57.284111+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/stream_client.go:182","msg":"[elevenlabs] start http request: url=https://api.elevenlabs.io/v1/text-to-speech/EXAVITQu4vr4xnSDxMaL/stream?optimize_streaming_latency=0&output_format=pcm_8000,locale=en-PH, speaker=en-PH-Sarah, text=I am a Pest Control OpenAI assistant. Before I transfer you, model_id=eleven_turbo_v2,voice_settings=map[similarity_boost:1 speed:1.2 stability:0.21]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-04-08T17:52:41.998238+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/stream_client.go:182","msg":"[elevenlabs] start http request: url=https://api.elevenlabs.io/v1/text-to-speech/EXAVITQu4vr4xnSDxMaL/stream?optimize_streaming_latency=2&output_format=pcm_8000,locale=en-PH, speaker=en-PH-Sarah, text=I am a Pest Control OpenAI assistant. Before I transfer you, model_id=eleven_turbo_v2,voice_settings=map[similarity_boost:1 speed:1.2 stability:1]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-02T19:08:25.5391+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:217","msg":"input handler context done","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-02T19:15:50.939879+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:217","msg":"input handler context done","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-02T19:20:24.41136+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:253","msg":"sending elevenlabs text: map[context_id:state-1 text:I am a Pest Control OpenAI assistant. Before I transfer you]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-02T19:20:54.036464+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:221","msg":"input handler context done","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-02T19:21:42.020006+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:253","msg":"sending elevenlabs text: map[context_id:state-1 text:I am a Pest Control OpenAI assistant. Before I transfer you]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-02T19:21:42.073064+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:276","msg":"output handler context done","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-02T19:23:02.874361+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:253","msg":"sending elevenlabs text: map[context_id:state-1 text:I am a Pest Control OpenAI assistant. Before I transfer you]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-02T19:24:53.811453+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:253","msg":"sending elevenlabs text: map[context_id:state-1 text:I am a Pest Control OpenAI assistant. Before I transfer you]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-02T19:25:12.384611+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:221","msg":"input handler context done","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-02T19:26:25.684052+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:253","msg":"sending elevenlabs text: map[context_id:state-1 text:I am a Pest Control OpenAI assistant. Before I transfer you]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-02T19:30:40.425057+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:251","msg":"sending elevenlabs text: map[context_id:state-1 text:I am a Pest Control OpenAI assistant. Before I transfer you]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-02T19:34:03.295305+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:254","msg":"sending elevenlabs text: map[context_id:state-1 text:I am a Pest Control OpenAI assistant. Before I transfer you]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-02T19:37:12.511729+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:256","msg":"sending elevenlabs text: map[context_id:state-1 text:I am a Pest Control OpenAI assistant. Before I transfer you]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-02T19:42:12.14083+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:224","msg":"input handler context done","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-03T10:21:14.395764+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:255","msg":"sending elevenlabs text: map[context_id:state-1 flush:true]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-03T10:21:44.013491+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:223","msg":"input handler context done","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-03T10:43:42.962019+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:223","msg":"input handler context done","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-03T11:15:13.172439+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:253","msg":"sending elevenlabs text: map[context_id:state-1 flush:true]","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"INFO","time":"2025-07-03T11:15:22.813666+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/multi_context_client.go:221","msg":"input handler context done","host_ip":"*************","host_name":"bdeMacBook-Pro.local"}
{"level":"ERROR","time":"2025-07-03T11:46:52.325744+08:00","caller":"/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/stream_client.go:407","msg":"read elevenlabs message failed: websocket: close 1008 (policy violation): Have not received a new text input within the timeout of 20 seconds. Streaming input terminated. Please make sure to eit","host_ip":"*************","host_name":"bdeMacBook-Pro.local","stack":"tts-service/internal/tts_service/service/elevenlabs.getStreamResponseByWebsocket.func1\n\t/Users/<USER>/airudder/tts-service/internal/tts_service/service/elevenlabs/stream_client.go:407"}
