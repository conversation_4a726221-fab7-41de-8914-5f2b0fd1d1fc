package elevenlabs

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/gorilla/websocket"
	"io"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/convert"
	"tts-service/internal/tts_service/service/params"
	"tts-service/pkg/pck2wav"
)

func TestStreamAudio(t *testing.T) {

	log.InitLogger()
	config.ElevenlabsDefaultKey = "********************************"
	config.SimilarityBoost = 100
	config.Stability = 100
	config.StreamingLatency = 2
	config.ElevenlabsWebSocketConfig = map[string]any{
		"test_websocket": true,
	}

	request := &models.TTSRequest{
		Version:      "eleven_flash_v2",
		TTSType:      "elevenlabs",
		Speaker:      "en-US-Sam",
		VoiceID:      "yoZ06aMxZJJ28mfd3POQ",
		Locale:       "en-US",
		Text:         "hello, this is a test for websocket",
		SpeakingRate: 1,
		Extra:        "",
		Stream:       true,
		RobotName:    "test_websocket",
	}

	ttsParams, err := params.NewTTSParams(request)
	fmt.Println(err)

	//ttsParams.Voice.TTSExtra = nil
	c := convert.EmptyAdapter(context.Background(), request, ttsParams, StreamAdapter)
	audioContent := make([]byte, 0)
	for response := range c {
		if response.Response != nil {
			fmt.Printf("after: %f, content=%d\n", response.Response.DelayTime, len(response.Response.AudioContent))
			audioContent = append(audioContent, response.Response.AudioContent...)
		} else {
			fmt.Println(response.Err)
		}
	}

	z, _ := pck2wav.Pcm2Wav(audioContent, 8000)
	name := "/Users/<USER>/Downloads/audio_pcm_0703_3" + time.Now().Format("20060102150405") + ".wav"
	file, _ := os.OpenFile(name, os.O_RDWR|os.O_CREATE, 0666)

	defer file.Close()
	_, _ = file.Write(z)

	_ = file.Sync()
}

func TestGetStreamResponseMultiContext(t *testing.T) {
	log.InitLogger()
	config.ElevenlabsDefaultKey = "********************************"
	config.SimilarityBoost = 100
	config.Stability = 100

	// Test case 1: Basic functionality - new connection
	t.Run("NewConnection", func(t *testing.T) {
		request := &models.TTSRequest{
			Version:      "eleven_flash_v2",
			TTSType:      "elevenlabs",
			Speaker:      "en-US-Sam",
			VoiceID:      "yoZ06aMxZJJ28mfd3POQ",
			Locale:       "en-US",
			Text:         "I am a Pest Control OpenAI assistant. Before I transfer you",
			SpeakingRate: 1,
			Extra:        "",
			CallId:       "test-call-1",
			StateID:      "state-1",
		}

		ttsParams, err := params.NewTTSParams(request)
		if err != nil {
			t.Fatalf("Failed to create TTS params: %v", err)
		}

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		audioChan := GetStreamResponseMultiContext(ctx, request, ttsParams)
		if audioChan == nil {
			t.Fatal("Expected non-nil audio channel")
		}

		// Collect audio data
		audioContent := make([]byte, 0)
		responseCount := 0

		for response := range audioChan {
			responseCount++
			if response.Err != nil {
				t.Logf("Received error: %v", response.Err)
				break
			}
			if response.Response != nil {
				t.Logf("Received audio chunk: %d bytes, delay: %f ms",
					len(response.Response.AudioContent), response.Response.DelayTime)
				audioContent = append(audioContent, response.Response.AudioContent...)
			}
		}

		if len(audioContent) == 0 {
			t.Error("Expected to receive some audio content")
		}

		t.Logf("Test completed. Received %d responses, total audio: %d bytes",
			responseCount, len(audioContent))

		z, _ := pck2wav.Pcm2Wav(audioContent, 8000)
		name := "/Users/<USER>/Downloads/audio_pcm_0703_4" + time.Now().Format("20060102150405") + ".wav"
		file, _ := os.OpenFile(name, os.O_RDWR|os.O_CREATE, 0666)

		defer file.Close()
		_, _ = file.Write(z)

		_ = file.Sync()

	})

	// Test case 2: Connection reuse
	t.Run("ConnectionReuse", func(t *testing.T) {
		callId := "test-call-reuse"

		request1 := &models.TTSRequest{
			Version:      "eleven_turbo_v2",
			TTSType:      "elevenlabs__v1",
			Speaker:      "en-PH-Sarah",
			VoiceID:      "EXAVITQu4vr4xnSDxMaL",
			Locale:       "en-PH",
			Text:         "First message",
			SpeakingRate: 1.2,
			Extra:        "{\"stability\":1.0}",
			Stream:       true,
			CallId:       callId,
			StateID:      "state-1",
		}

		request2 := &models.TTSRequest{
			Version:      "eleven_turbo_v2",
			TTSType:      "elevenlabs__v1",
			Speaker:      "en-PH-Sarah",
			VoiceID:      "EXAVITQu4vr4xnSDxMaL",
			Locale:       "en-PH",
			Text:         "Second message",
			SpeakingRate: 1.2,
			Extra:        "{\"stability\":1.0}",
			Stream:       true,
			CallId:       callId,
			StateID:      "state-2",
		}

		ttsParams1, err := params.NewTTSParams(request1)
		if err != nil {
			t.Fatalf("Failed to create TTS params 1: %v", err)
		}

		ttsParams2, err := params.NewTTSParams(request2)
		if err != nil {
			t.Fatalf("Failed to create TTS params 2: %v", err)
		}

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// First call - should create new connection
		audioChan1 := GetStreamResponseMultiContext(ctx, request1, ttsParams1)
		if audioChan1 == nil {
			t.Fatal("Expected non-nil audio channel for first call")
		}

		// Wait a bit for connection to be established
		time.Sleep(2 * time.Second)

		// Second call - should reuse existing connection
		audioChan2 := GetStreamResponseMultiContext(ctx, request2, ttsParams2)
		if audioChan2 == nil {
			t.Fatal("Expected non-nil audio channel for second call")
		}

		// Both channels should be the same (connection reuse)
		if audioChan1 != audioChan2 {
			t.Error("Expected same audio channel for connection reuse")
		}

		// Collect some audio from the shared channel
		audioContent := make([]byte, 0)
		responseCount := 0
		timeout := time.After(10 * time.Second)

	collectLoop:
		for {
			select {
			case response, ok := <-audioChan1:
				if !ok {
					break collectLoop
				}
				responseCount++
				if response.Err != nil {
					t.Logf("Received error: %v", response.Err)
					break collectLoop
				}
				if response.Response != nil {
					audioContent = append(audioContent, response.Response.AudioContent...)
					if len(audioContent) > 500 {
						break collectLoop
					}
				}
			case <-timeout:
				t.Log("Test timeout reached")
				break collectLoop
			}
		}

		t.Logf("Connection reuse test completed. Received %d responses, total audio: %d bytes",
			responseCount, len(audioContent))

		// Clean up
		DeleteElevenlabsConn(callId)
	})

	// Test case 3: Context cancellation
	t.Run("ContextCancellation", func(t *testing.T) {
		request := &models.TTSRequest{
			Version:      "eleven_turbo_v2",
			TTSType:      "elevenlabs__v1",
			Speaker:      "en-PH-Sarah",
			VoiceID:      "EXAVITQu4vr4xnSDxMaL",
			Locale:       "en-PH",
			Text:         "This message should be cancelled",
			SpeakingRate: 1.2,
			Extra:        "{\"stability\":1.0}",
			Stream:       true,
			CallId:       "test-call-cancel",
			StateID:      "state-cancel",
		}

		ttsParams, err := params.NewTTSParams(request)
		if err != nil {
			t.Fatalf("Failed to create TTS params: %v", err)
		}

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)

		audioChan := GetStreamResponseMultiContext(ctx, request, ttsParams)
		if audioChan == nil {
			t.Fatal("Expected non-nil audio channel")
		}

		// Cancel context after a short delay
		go func() {
			time.Sleep(2 * time.Second)
			cancel()
		}()

		// Should receive cancellation error
		errorReceived := false
		for response := range audioChan {
			if response.Err != nil {
				t.Logf("Received expected error after cancellation: %v", response.Err)
				errorReceived = true
				break
			}
		}

		if !errorReceived {
			t.Error("Expected to receive cancellation error")
		}

		// Clean up
		DeleteElevenlabsConn(request.CallId)
	})

	// Test case 3: Concurrent access
	t.Run("ConcurrentAccess", func(t *testing.T) {
		callId := "test-call-concurrent"
		numGoroutines := 3

		ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
		defer cancel()

		// Channel to collect results from goroutines
		results := make(chan bool, numGoroutines)

		for i := 0; i < numGoroutines; i++ {
			go func(id int) {
				request := &models.TTSRequest{
					Version:      "eleven_turbo_v2",
					TTSType:      "elevenlabs__v1",
					Speaker:      "en-PH-Sarah",
					VoiceID:      "EXAVITQu4vr4xnSDxMaL",
					Locale:       "en-PH",
					Text:         fmt.Sprintf("Concurrent message %d", id),
					SpeakingRate: 1.2,
					Extra:        "{\"stability\":1.0}",
					Stream:       true,
					CallId:       callId,
					StateID:      fmt.Sprintf("state-%d", id),
				}

				ttsParams, err := params.NewTTSParams(request)
				if err != nil {
					t.Errorf("Failed to create TTS params for goroutine %d: %v", id, err)
					results <- false
					return
				}

				audioChan := GetStreamResponseMultiContext(ctx, request, ttsParams)
				if audioChan == nil {
					t.Errorf("Expected non-nil audio channel for goroutine %d", id)
					results <- false
					return
				}

				// Collect some responses
				responseCount := 0
				timeout := time.After(5 * time.Second)

			collectLoop:
				for {
					select {
					case response, ok := <-audioChan:
						if !ok {
							break collectLoop
						}
						responseCount++
						if response.Err != nil {
							t.Logf("Goroutine %d received error: %v", id, response.Err)
							break collectLoop
						}
						if responseCount >= 2 { // Collect a few responses
							break collectLoop
						}
					case <-timeout:
						break collectLoop
					}
				}

				t.Logf("Goroutine %d completed with %d responses", id, responseCount)
				results <- true
			}(i)
		}

		// Wait for all goroutines to complete
		successCount := 0
		for i := 0; i < numGoroutines; i++ {
			if <-results {
				successCount++
			}
		}

		t.Logf("Concurrent test completed. %d/%d goroutines succeeded", successCount, numGoroutines)

		if successCount == 0 {
			t.Error("Expected at least one goroutine to succeed")
		}

		// Clean up
		DeleteElevenlabsConn(callId)
	})
}

// TestElevenlabsConnMethods tests the connection management methods
func TestElevenlabsConnMethods(t *testing.T) {
	// Test setLastState and getLastState
	t.Run("StateManagement", func(t *testing.T) {
		conn := &ElevenlabsConn{}

		// Test initial state
		if state := conn.getLastState(); state != "" {
			t.Errorf("Expected empty initial state, got: %s", state)
		}

		// Test setting and getting state
		testState := "test-state-123"
		conn.setLastState(testState)

		if state := conn.getLastState(); state != testState {
			t.Errorf("Expected state %s, got: %s", testState, state)
		}
	})

	// Test connection storage and retrieval
	t.Run("ConnectionStorage", func(t *testing.T) {
		callId := "test-storage-call"

		// Test getting non-existent connection
		if conn := GetElevenlabsConn(callId); conn != nil {
			t.Error("Expected nil for non-existent connection")
		}

		// Create and store connection
		testConn := &ElevenlabsConn{
			lastState: "test-state",
		}
		SetElevenlabsConn(callId, testConn)

		// Test retrieval
		retrievedConn := GetElevenlabsConn(callId)
		if retrievedConn == nil {
			t.Fatal("Expected to retrieve stored connection")
		}
		if retrievedConn.getLastState() != "test-state" {
			t.Error("Retrieved connection has incorrect state")
		}

		// Test deletion
		DeleteElevenlabsConn(callId)
		if conn := GetElevenlabsConn(callId); conn != nil {
			t.Error("Expected nil after deletion")
		}
	})
}

// TestConnectionCleanup tests proper resource cleanup
func TestConnectionCleanup(t *testing.T) {
	t.Run("ProperCleanup", func(t *testing.T) {
		callId := "test-cleanup-call"

		// Create connection with channels
		ctx, cancel := context.WithCancel(context.Background())
		conn := &ElevenlabsConn{
			input:  make(chan map[string]interface{}, 1),
			output: make(chan *convert.StreamAudio, 1),
			error:  make(chan error, 1),
			ctx:    ctx,
			cancel: cancel,
		}

		SetElevenlabsConn(callId, conn)

		// Verify connection exists
		if GetElevenlabsConn(callId) == nil {
			t.Fatal("Connection should exist before cleanup")
		}

		// Delete connection (should trigger cleanup)
		DeleteElevenlabsConn(callId)

		// Verify connection is removed
		if GetElevenlabsConn(callId) != nil {
			t.Error("Connection should be removed after cleanup")
		}

		// Verify context is cancelled
		select {
		case <-ctx.Done():
			// Expected
		case <-time.After(1 * time.Second):
			t.Error("Context should be cancelled after cleanup")
		}
	})
}

func TestDoubleStreamAudio(t *testing.T) {
	log.InitLogger()
	config.ElevenlabsDefaultKey = "********************************"

	request := &models.TTSRequest{
		Speaker: "21m00Tcm4TlvDq8ikWAM",
		Locale:  "eleven_monolingual_v1",
	}

	ttsParams := &params.TtsParams{
		Input: &params.InputParams{
			SSML: "I am a Pest Control AI assistant. (Before) I transfer you",
		},
	}
	c := convert.PcmConvertAdapter(context.Background(), request, ttsParams, func(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, inPipe io.WriteCloser) error {
		senderC, receiverC, errorC, err := GetDoubleStreamResponse(ctx, request, ttsParams)
		if err != nil {
			t.Fatalf("get response failed: %s", err.Error())
		}

		go func() {
			for _, text := range strings.Split("I am a Pest Control AI assistant. (Before) I transfer you", " ") {
				senderC <- text + " "
				time.Sleep(10 * time.Millisecond)
			}

			close(senderC)
		}()

		for {
			select {
			case content, ok := <-receiverC:
				if content == nil && !ok {
					return nil
				}
				_, err = inPipe.Write(content)
				if err != nil {
					t.Fatalf("write content failed: %s", err.Error())
				}
			case err := <-errorC:
				t.Fatalf("get response failed: %s", err.Error())
			}
		}

		return nil
	})
	audioContent := make([]byte, 0)
	for response := range c {
		if response.Response != nil {
			audioContent = append(audioContent, response.Response.AudioContent...)
		} else {
			fmt.Println(response.Err)
		}
	}

	z, _ := pck2wav.Pcm2Wav(audioContent, 8000)
	file, _ := os.OpenFile("/Users/<USER>/Desktop/audio_pcm_0.wav", os.O_WRONLY|os.O_CREATE, 0666)

	defer file.Close()
	_, _ = file.Write(z)

	_ = file.Sync()
}

func TestDoubleStreamAudio2(t *testing.T) {
	startTime := time.Now()
	url := fmt.Sprintf("wss://api.elevenlabs.io/v1/text-to-speech/%s/stream-input?model_id=%s", "21m00Tcm4TlvDq8ikWAM", "eleven_monolingual_v1")
	conn, _, err := websocket.DefaultDialer.Dial(url, http.Header{
		"xi-api-key": []string{"********************************"},
	})

	if err != nil {
		t.Fatalf("dial: %v", err)
	}

	bos := map[string]interface{}{
		"text":                   " ",
		"try_trigger_generation": true,
		"voice_settings": map[string]interface{}{
			"stability":        0.5,
			"similarity_boost": 0.5,
		},
		"generation_config": map[string]interface{}{
			"chunk_length_schedule": []int{50},
		},
	}

	eos := map[string]interface{}{
		"text": "",
	}

	if err := conn.WriteJSON(bos); err != nil {
		t.Fatalf("write bos: %v", err)
	}

	go func() {
		texts := "Hey, this is Customer Service calling back on your pest control request a couple of minutes ago. Sorry, we missed you. Please give us a call back at (559)-550-5353 to discuss your pest control needs."
		for _, text := range strings.Split(texts, " ") {
			bos["text"] = text + " "
			if err := conn.WriteJSON(bos); err != nil {
				t.Fatalf("write text: %v", err)
			}

			time.Sleep(10 * time.Millisecond)
		}

		if err := conn.WriteJSON(eos); err != nil {
			t.Fatalf("write eos: %v", err)
		}
	}()

	var audioContent []byte
	for {
		_, message, err := conn.ReadMessage()
		if err == io.EOF || websocket.IsCloseError(err, websocket.CloseNormalClosure) {
			break
		}

		if err != nil {
			t.Fatalf("read: %v", err)
		}

		var response struct {
			AudioContent []byte `json:"audio"`
		}

		if err = json.Unmarshal(message, &response); err != nil {
			t.Fatalf("unmarshal: %v", err)
		}

		audioContent = append(audioContent, response.AudioContent...)
		fmt.Println(len(audioContent))

		fmt.Println(time.Now().Sub(startTime))
	}

	file, _ := os.OpenFile("/Users/<USER>/Desktop/ds_audio.wav", os.O_WRONLY|os.O_CREATE, 0666)

	defer file.Close()
	_, _ = file.Write(audioContent)

	_ = file.Sync()
}

func TestPcmToWav(t *testing.T) {
	audioContent, err := os.ReadFile("/Users/<USER>/Desktop/output.pcm")
	if err != nil {
		t.Fatalf("read file: %v", err)
	}

	file, _ := os.OpenFile("/Users/<USER>/Desktop/example_1.wav", os.O_WRONLY|os.O_CREATE, 0666)

	defer file.Close()
	z, _ := pck2wav.Pcm2Wav(audioContent, 8000)
	_, _ = file.Write(z)

	_ = file.Sync()
}

type BufferWriteCloser struct {
	buffer bytes.Buffer
}

func (bwc *BufferWriteCloser) Write(p []byte) (n int, err error) {
	return bwc.buffer.Write(p)
}

func (bwc *BufferWriteCloser) Close() error {
	// bytes.Buffer 不需要关闭，所以这里什么都不做
	return nil
}

func TestOriginalAudio(t *testing.T) {
	log.InitLogger()
	config.ElevenlabsDefaultKey = "********************************"
	config.Stability = 100
	config.SimilarityBoost = 100

	request := &models.TTSRequest{
		Speaker: "21m00Tcm4TlvDq8ikWAM",
		Locale:  "eleven_turbo_v2",
	}

	ttsParams := &params.TtsParams{
		Input: &params.InputParams{
			SSML: "I am a Pest Control OpenAI assistant. Before I transfer you",
		},
	}

	buffer := &BufferWriteCloser{}
	err := StreamAdapter(context.Background(), request, ttsParams, buffer)
	if err != nil {
		t.Fatalf("get response failed: %s", err.Error())
	}

	if err != nil {
		t.Fatalf("get response failed: %s", err.Error())
	}

	z, _ := io.ReadAll(&buffer.buffer)
	z, _ = pck2wav.Pcm2Wav(z, 16000)
	file, _ := os.OpenFile("/Users/<USER>/Desktop/pcm16.wav", os.O_WRONLY|os.O_CREATE, 0666)

	defer file.Close()
	file.Write(z)

	_ = file.Sync()
}
