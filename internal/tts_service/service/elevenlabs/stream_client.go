package elevenlabs

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/gorilla/websocket"
	"github.com/pkg/errors"
	"io"
	"net/http"
	"strings"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/constants"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/convert"
	"tts-service/internal/tts_service/service/params"
)

var textSplitters = map[rune]struct{}{
	'.': {},
	',': {},
	'?': {},
	'!': {},
	';': {},
	':': {},
	'—': {},
	'-': {},
	'(': {},
	')': {},
	'[': {},
	']': {},
	'}': {},
	' ': {},
}

type DoubleStreamVoiceSettings struct {
	Stability       float64 `json:"stability,omitempty"`
	SimilarityBoost float64 `json:"similarity_boost,omitempty"`
	Speed           float64 `json:"speed,omitempty"`
}

type DoubleStreamConfig struct {
	ChunkLengthSchedule []int `json:"chunk_length_schedule"`
}

type DoubleStreamRequest struct {
	Text                 string                     `json:"text"`
	TryTriggerGeneration bool                       `json:"try_trigger_generation,omitempty"`
	VoiceSettings        *DoubleStreamVoiceSettings `json:"voice_settings ,omitempty"`
	GenerationConfig     *DoubleStreamConfig        `json:"generation_config,omitempty"`
	Flush                bool                       `json:"flush,omitempty"`
}

type DoubleStreamResponse struct {
	AudioContent []byte `json:"audio"`
}

type WebsocketStreamResponse struct {
	Audio   string `json:"audio"`
	IsFinal bool   `json:"is_final"`
}

var (
	doubleStreamEosMessage = map[string]string{
		"text": "",
	}
)

func GetDoubleStreamResponse(ctx context.Context, request *models.TTSRequest, params *params.TtsParams) (chan string, chan []byte, chan error, error) {
	provider := constants.ElevenLabsProvider
	url := fmt.Sprintf(
		"wss://api.elevenlabs.io/v1/text-to-speech/%s/stream-input?model_id=%s",
		params.ConvertSpeaker(request.Speaker, provider),
		params.GetModel(),
	)

	conn, _, err := websocket.DefaultDialer.DialContext(ctx, url, http.Header{
		"xi-api-key": []string{params.GetElevenlabsKey(request.Speaker)},
	})

	if err != nil {
		return nil, nil, nil, errors.Wrap(err, "connect to elevenlabs websocket failed")
	}

	message := DoubleStreamRequest{
		Text:                 " ",
		TryTriggerGeneration: true,
		VoiceSettings: &DoubleStreamVoiceSettings{
			Stability:       float64(config.Stability) / 100,
			SimilarityBoost: float64(config.SimilarityBoost) / 100,
		},
		GenerationConfig: &DoubleStreamConfig{
			ChunkLengthSchedule: []int{50},
		},
	}

	if err := conn.WriteJSON(message); err != nil {
		return nil, nil, nil, errors.Wrap(err, "write elevenlabs bos failed")
	}

	textChan := make(chan string, 10)
	audioChan := make(chan []byte, 10)
	errorChan := make(chan error, 1)
	go func() {
		for text := range textChan {
			if len(text) == 0 {
				continue
			}

			if text[len(text)-1] != ' ' {
				text += " "
			}

			log.Infof(ctx, "接收到新的 elevenlabs 字符：%s", text)
			message.Text = text

			if err := conn.WriteJSON(message); err != nil {
				errorChan <- errors.Wrap(err, "write elevenlabs text failed")
				return
			}
		}

		if err := conn.WriteJSON(doubleStreamEosMessage); err != nil {
			errorChan <- errors.Wrap(err, "write elevenlabs eos message failed")
			return
		}
	}()

	go func() {
		defer func() {
			close(audioChan)
		}()
		for {
			_, message, err := conn.ReadMessage()
			if err == io.EOF || websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseAbnormalClosure) {
				return
			}

			if err != nil {
				errorChan <- errors.Wrap(err, "read elevenlabs message failed")
				return
			}

			response := DoubleStreamResponse{}
			if err = json.Unmarshal(message, &response); err != nil {
				errorChan <- errors.Wrap(err, "unmarshal elevenlabs message failed")
				return
			}

			audioChan <- response.AudioContent
		}
	}()

	return textChan, audioChan, errorChan, nil
}

func GetStreamResponse(ctx context.Context, request *models.TTSRequest, params *params.TtsParams) (io.ReadCloser, error) {
	if _, ok := config.ElevenlabsWebSocketConfig[strings.ToLower(request.RobotName)]; ok {
		return getStreamResponseByWebsocket(ctx, request, params)
	}

	if _, ok := config.ElevenlabsWebSocketConfig[request.AccountType]; ok {
		return getStreamResponseByWebsocket(ctx, request, params)
	}

	return getSteamResponse(ctx, request, params)
}

func getSteamResponse(ctx context.Context, request *models.TTSRequest, params *params.TtsParams) (io.ReadCloser, error) {
	provider := constants.ElevenLabsProvider
	url := fmt.Sprintf(
		"https://api.elevenlabs.io/v1/text-to-speech/%s/stream?optimize_streaming_latency=%d&output_format=pcm_8000",
		params.ConvertSpeaker(request.Speaker, provider),
		config.StreamingLatency,
	)
	headers := map[string]string{
		"Accept":       "audio/mpeg",
		"Content-Type": "application/json",
		"xi-api-key":   params.GetElevenlabsKey(request.Speaker),
	}

	data := map[string]interface{}{
		"text":     params.GetTtsSSML(request, provider),
		"model_id": params.GetModel(),
		"voice_settings": map[string]interface{}{
			"stability":        params.GetStability(),
			"similarity_boost": float64(config.SimilarityBoost) / 100,
			"speed":            params.GetSpeed(),
		},
	}

	if applyTextNormalization := params.GetApplyTextNormalization(); applyTextNormalization != nil {
		data["apply_text_normalization"] = *applyTextNormalization
	}

	if languageCode := params.GetLanguageCode(); languageCode != nil && *languageCode {
		data["language_code"] = params.GetISO639Language()
	}

	log.Infof(
		ctx,
		"[elevenlabs] start http request: url=%s,locale=%s, speaker=%s, text=%s, model_id=%s, applyText:%v,language_code:%v,voice_settings=%+v",
		url,
		request.Locale,
		request.Speaker,
		data["text"],
		data["model_id"],
		data["apply_text_normalization"], data["language_code"],
		data["voice_settings"],
	)
	jsonData, _ := json.Marshal(data)
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "connect to elevenlabs failed")
	}

	if resp.StatusCode != 200 {
		return nil, errors.Errorf("elevenlabs response status code is %d", resp.StatusCode)
	}

	return resp.Body, nil
}

func DoubleStreamAdapter(textChan chan string) convert.Adapter {
	return func(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, inPipe io.WriteCloser) error {
		senderC, receiverC, errorC, err := GetDoubleStreamResponse(ctx, request, ttsParams)
		if err != nil {
			return err
		}

		go func() {
			var buffer string
			for text := range textChan {
				if text == "" {
					continue
				}

				hasSplit := false
				if len(buffer) > 0 {
					if _, ok := textSplitters[rune(buffer[len(buffer)-1])]; ok {
						hasSplit = true
					}
				}

				if hasSplit {
					senderC <- buffer
					buffer = text
				} else if _, ok := textSplitters[rune(text[0])]; ok {
					buffer = buffer + string(text[0])
					senderC <- buffer
					buffer = text[1:]
				} else {
					buffer += text
				}
			}

			if buffer != "" {
				senderC <- buffer
			}

			close(senderC)
		}()

		for {
			select {
			case content, ok := <-receiverC:
				if content == nil && !ok {
					return nil
				}

				_, err := inPipe.Write(content)
				if err != nil {
					return err
				}
			case err := <-errorC:
				return err
			}
		}
	}
}

func StreamAdapter(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, inPipe io.WriteCloser) error {
	content, err := GetStreamResponse(ctx, request, ttsParams)

	if err != nil {
		log.Infof(ctx, "[%s] get response failed: %s", request.TTSType, err.Error())
		return err
	}
	defer content.Close()

	buffer := make([]byte, 4096)
	size := 0
	for {
		n, err := content.Read(buffer)
		if err == io.EOF {
			break
		}

		if err != nil {
			return err
		}

		size += n
		_, _ = inPipe.Write(buffer[:n])
	}

	return nil
}

func handleText(text string) string {
	if len(text) == 0 {
		return " "
	}

	if text[len(text)-1] != ' ' {
		text += " "
	}
	return text
}

type chanReader struct {
	ch chan []byte
}

func (c chanReader) Read(p []byte) (n int, err error) {
	for {
		select {
		case b, ok := <-c.ch:
			if !ok {
				return 0, io.EOF
			}
			n = copy(p, b)
			return n, nil
		}
	}
}

func (c chanReader) Close() error {
	return nil
}

func getStreamResponseByWebsocket(ctx context.Context, request *models.TTSRequest, params *params.TtsParams) (io.ReadCloser, error) {
	provider := constants.ElevenLabsProvider
	url := fmt.Sprintf(
		"wss://api.elevenlabs.io/v1/text-to-speech/%s/stream-input?model_id=%s&output_format=pcm_8000",
		params.ConvertSpeaker(request.Speaker, provider),
		params.GetModel(),
	)

	conn, _, err := websocket.DefaultDialer.DialContext(ctx, url, http.Header{
		"xi-api-key": []string{params.GetElevenlabsKey(request.Speaker)},
	})

	if err != nil {
		return nil, errors.Wrap(err, "connect to elevenlabs websocket failed")
	}

	message := DoubleStreamRequest{
		Text: handleText(params.GetTtsSSML(request, provider)),
		VoiceSettings: &DoubleStreamVoiceSettings{
			Stability:       params.GetStability(),
			SimilarityBoost: float64(config.SimilarityBoost) / 100,
			Speed:           params.GetSpeed(),
		},
		//GenerationConfig: DoubleStreamConfig{
		//	ChunkLengthSchedule: []int{50},
		//},
		Flush: true,
	}

	s, _ := json.Marshal(message)
	fmt.Println("message:", string(s))

	if err := conn.WriteJSON(message); err != nil {
		return nil, errors.Wrap(err, "write elevenlabs initial message failed")
	}

	closeMessage := map[string]string{
		"text": "",
	}

	if err := conn.WriteJSON(closeMessage); err != nil {
		return nil, errors.Wrap(err, "write elevenlabs close message failed")
	}

	audioChan := make(chan []byte, 10)
	go func() {
		defer func() {
			close(audioChan)
			_ = conn.Close()
		}()
		for {
			_, message, err := conn.ReadMessage()
			if err == io.EOF || websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseAbnormalClosure) {
				return
			}

			if err != nil {
				log.Errorf(ctx, "read elevenlabs message failed: %s", err.Error())
				return
			}

			response := WebsocketStreamResponse{}
			if err = json.Unmarshal(message, &response); err != nil {
				log.Errorf(ctx, "unmarshal elevenlabs message failed: %s", err.Error())
				return
			}

			audioData, err := base64.StdEncoding.DecodeString(response.Audio)
			if err != nil {
				log.Errorf(ctx, "elevenlabs base64 decode failed: %s", err.Error())
				continue
			}

			fmt.Println("getlen:", len(audioData))
			audioChan <- audioData

			if response.IsFinal {
				return
			}

		}
	}()

	//channel 处理成可以读取的io.ReadCloser
	return &chanReader{ch: audioChan}, nil

}
