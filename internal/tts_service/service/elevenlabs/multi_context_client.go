package elevenlabs

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"sync"
	"time"
	"tts-service/internal/pkg/config"

	"github.com/gorilla/websocket"
	"github.com/pkg/errors"
	"net/http"

	"tts-service/internal/pkg/code"
	"tts-service/internal/pkg/constants"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/convert"
	"tts-service/internal/tts_service/service/params"
)

var (
	elevenlabsConnMap sync.Map
)

// ElevenlabsConn represents a WebSocket connection to ElevenLabs with associated channels
type ElevenlabsConn struct {
	conn      *websocket.Conn
	input     chan map[string]interface{}
	output    chan *convert.StreamAudio
	error     chan error
	lastState string
	mu        sync.RWMutex // protects lastState
	ctx       context.Context
	cancel    context.CancelFunc
}

// APIResponse represents the response structure from ElevenLabs WebSocket API
type APIResponse struct {
	ContextID string `json:"contextId"`
	Audio     string `json:"audio,omitempty"`
	IsFinal   bool   `json:"is_final,omitempty"`
}

// setLastState safely sets the last state with mutex protection
func (conn *ElevenlabsConn) setLastState(state string) {
	conn.mu.Lock()
	defer conn.mu.Unlock()
	conn.lastState = state
}

// getLastState safely gets the last state with mutex protection
func (conn *ElevenlabsConn) getLastState() string {
	conn.mu.RLock()
	defer conn.mu.RUnlock()
	return conn.lastState
}

// close safely closes the connection and associated resources
func (conn *ElevenlabsConn) close() {
	if conn.cancel != nil {
		conn.cancel()
	}
	if conn.conn != nil {
		_ = conn.conn.Close()
	}
	if conn.input != nil {
		close(conn.input)
	}
	if conn.output != nil {
		close(conn.output)
	}
}

// SetElevenlabsConn stores a connection in the global connection map
func SetElevenlabsConn(callId string, conn *ElevenlabsConn) {
	elevenlabsConnMap.Store(callId, conn)
}

// GetElevenlabsConn retrieves a connection from the global connection map
func GetElevenlabsConn(callId string) *ElevenlabsConn {
	conn, ok := elevenlabsConnMap.Load(callId)
	if !ok {
		return nil
	}
	return conn.(*ElevenlabsConn)
}

// DeleteElevenlabsConn removes a connection from the global connection map and closes it
func DeleteElevenlabsConn(callId string) {
	if conn := GetElevenlabsConn(callId); conn != nil {
		conn.close()
	}
	elevenlabsConnMap.Delete(callId)
}

// GetStreamResponseMultiContext handles multi-context streaming for ElevenLabs TTS
func GetStreamResponseMultiContext(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams) <-chan *convert.StreamAudio {
	// Check if connection already exists for this call
	if connManager := GetElevenlabsConn(request.CallId); connManager != nil {
		// Send text to existing connection
		select {
		case connManager.input <- map[string]interface{}{
			"text":       handleText(ttsParams.GetTtsSSML(request, constants.ElevenLabsProvider)),
			"context_id": request.StateID,
			"flush":      true,
		}:
			return connManager.output
		case <-ctx.Done():
			// Context cancelled, return error channel
			errorChan := make(chan *convert.StreamAudio, 1)
			errorChan <- &convert.StreamAudio{
				Err:       ctx.Err(),
				ErrorCode: code.ErrorContextCanceled,
			}
			close(errorChan)
			return errorChan
		case <-time.After(10 * time.Second):
			// Timeout sending to input channel, connection might be stuck
			log.Errorf(ctx, "timeout sending text to existing connection for callID: %s", request.CallId)
			DeleteElevenlabsConn(request.CallId)
			// Fall through to create new connection
		}
	}

	// Create new connection
	return createNewConnection(ctx, request, ttsParams)
}

// createNewConnection establishes a new WebSocket connection to ElevenLabs
func createNewConnection(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams) <-chan *convert.StreamAudio {
	responseChan := make(chan *convert.StreamAudio, 100)
	textChan := make(chan map[string]interface{}, 5)

	provider := constants.ElevenLabsProvider
	url := fmt.Sprintf(
		"wss://api.elevenlabs.io/v1/text-to-speech/%s/multi-stream-input?model_id=%s&output_format=pcm_8000",
		ttsParams.ConvertSpeaker(request.Speaker, provider),
		ttsParams.GetModel(),
	)

	conn, _, err := websocket.DefaultDialer.DialContext(ctx, url, http.Header{
		"xi-api-key": []string{ttsParams.GetElevenlabsKey(request.Speaker)},
	})

	if err != nil {
		err = errors.Wrap(err, fmt.Sprintf("connect to elevenlabs websocket failed, callID: %s", request.CallId))
		responseChan <- &convert.StreamAudio{
			Err:       err,
			ErrorCode: code.ErrorInternal,
		}
		close(responseChan)
		return responseChan
	}

	// Send initial message with voice settings
	message := map[string]interface{}{
		"text": handleText(ttsParams.GetTtsSSML(request, provider)),
		"voice_settings": map[string]interface{}{
			"stability":        ttsParams.GetStability(),
			"similarity_boost": float64(config.SimilarityBoost) / 100,
			"speed":            ttsParams.GetSpeed(),
		},
		"context_id": request.StateID,
	}

	s, _ := json.Marshal(message)
	fmt.Println("message:", string(s))

	if err := conn.WriteJSON(message); err != nil {
		err = errors.Wrap(err, fmt.Sprintf("write elevenlabs initial message failed, callID: %s", request.CallId))
		responseChan <- &convert.StreamAudio{
			Err:       err,
			ErrorCode: code.ErrorInternal,
		}
		close(responseChan)
		_ = conn.Close()
		return responseChan
	}

	// Create connection context with cancellation
	connCtx, cancel := context.WithCancel(ctx)

	// Create and store connection manager
	elevenlabsConn := &ElevenlabsConn{
		conn:      conn,
		input:     textChan,
		output:    responseChan,
		error:     make(chan error, 1),
		lastState: request.StateID,
		ctx:       connCtx,
		cancel:    cancel,
	}
	SetElevenlabsConn(request.CallId, elevenlabsConn)

	// Start input processing goroutine
	go handleInputMessages(connCtx, elevenlabsConn, conn)

	// Start output processing goroutine
	go handleOutputMessages(connCtx, elevenlabsConn, conn, request.CallId)
	textChan <- map[string]interface{}{
		"context_id": request.StateID,
		"flush":      true, //测试发现，不flush，不会主动返回audio
	}
	return responseChan
}

// handleInputMessages processes incoming text messages and sends them to the WebSocket
func handleInputMessages(ctx context.Context, elevenlabsConn *ElevenlabsConn, conn *websocket.Conn) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf(ctx, "panic in handleInputMessages: %v", r)
		}
	}()

	for {
		select {
		case <-ctx.Done():
			log.Infof(ctx, "input handler context done")
			return
		case text, ok := <-elevenlabsConn.input:
			if !ok {
				log.Infof(ctx, "input channel closed")
				return
			}

			if len(text) == 0 {
				continue
			}

			// Check if we need to flush previous context
			//lastState := elevenlabsConn.getLastState()
			//if contextID, exists := text["context_id"]; exists && lastState != "" && lastState != contextID {
			//	flushMsg := map[string]interface{}{
			//		"flush":      true,
			//		"context_id": lastState,
			//	}
			//	if err := conn.WriteJSON(flushMsg); err != nil {
			//		log.Errorf(ctx, "write elevenlabs flush failed: %s", err.Error())
			//		continue
			//	}
			//}

			// Update last state
			if contextID, exists := text["context_id"]; exists {
				if contextIDStr, ok := contextID.(string); ok {
					elevenlabsConn.setLastState(contextIDStr)
				}
			}

			log.Infof(ctx, "sending elevenlabs text: %v", text)
			s, _ := json.Marshal(text)
			fmt.Println("message:", string(s))
			if err := conn.WriteJSON(text); err != nil {
				log.Errorf(ctx, "write elevenlabs text failed: %s", err.Error())
				return
			}
		}
	}
}

// handleOutputMessages processes WebSocket responses and sends audio data to the output channel
func handleOutputMessages(ctx context.Context, elevenlabsConn *ElevenlabsConn, conn *websocket.Conn, callID string) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf(ctx, "panic in handleOutputMessages: %v", r)
		}
		DeleteElevenlabsConn(callID)
	}()

	startTime := time.Now()

	for {
		select {
		case <-ctx.Done():
			log.Infof(ctx, "output handler context done")
			return
		default:
			_, message, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseNormalClosure, websocket.CloseGoingAway) {
					log.Errorf(ctx, "elevenlabs websocket unexpected close: %s", err.Error())
					elevenlabsConn.output <- &convert.StreamAudio{
						Err:       err,
						ErrorCode: code.ErrorContextCanceled,
					}
				}
				return
			}

			var resp APIResponse
			if err := json.Unmarshal(message, &resp); err != nil {
				log.Errorf(ctx, "elevenlabs unmarshal message failed: %s", err.Error())
				continue
			}

			// Process audio data
			if resp.Audio != "" {
				audioData, err := base64.StdEncoding.DecodeString(resp.Audio)
				if err != nil {
					log.Errorf(ctx, "elevenlabs base64 decode failed: %s", err.Error())
					continue
				}

				item := &convert.StreamAudio{
					Response: &params.ClientResponse{
						AudioContent: audioData,
						Code:         code.ServerOK,
						Duration:     int64(len(audioData)/16) * 1000,
						DelayTime:    float64(time.Now().Sub(startTime).Milliseconds()),
					},
				}
				startTime = time.Now()

				select {
				case elevenlabsConn.output <- item:
				case <-ctx.Done():
					return
				}
			}

			// Handle context completion notification
			if resp.IsFinal {
				log.Infof(ctx, "received final response for context: %s", resp.ContextID)
			}
		}
	}
}
