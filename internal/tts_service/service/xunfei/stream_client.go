package xunfei

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/gorilla/websocket"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/constants"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
)

const (
	//hostUrl   = "https://eu-central-1.aicloudapi.com/v2/tts"
	hostUrl = "https://tts-api-sg.xf-yun.com/v2/tts" //test
)

var jsonObj = json.NewDecoder(nil)

type ResponseData struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Sid     string `json:"sid"`
	Data    struct {
		Status int    `json:"status"`
		Audio  string `json:"audio"`
		Ced    string `json:"ced"`
	} `json:"data"`
}

func getAuthUrl(hostUrl, apiKey, apiSecret string) (string, error) {
	u, err := url.Parse(hostUrl)
	if err != nil {
		return "", err
	}

	date := time.Now().UTC().Format(http.TimeFormat)

	builder := fmt.Sprintf("host: %s\ndate: %s\nGET %s HTTP/1.1", u.Host, date, u.Path)

	mac := hmac.New(sha256.New, []byte(apiSecret))
	mac.Write([]byte(builder))
	sha := base64.StdEncoding.EncodeToString(mac.Sum(nil))

	authorization := fmt.Sprintf(`hmac username="%s", algorithm="hmac-sha256", headers="host date request-line", signature="%s"`, apiKey, sha)

	queryParams := url.Values{}
	queryParams.Add("authorization", base64.StdEncoding.EncodeToString([]byte(authorization)))
	queryParams.Add("date", date)
	queryParams.Add("host", u.Host)

	authUrl := fmt.Sprintf("https://%s%s?%s", u.Host, u.Path, queryParams.Encode())
	return authUrl, nil
}

func GetStreamResponse(
	ctx context.Context,
	request *models.TTSRequest,
	params *params.TtsParams,
) (chan []byte, chan error, error) {

	provider := constants.XunfeiProvider
	authUrl, err := getAuthUrl(config.XunfeiHost, config.XunfeiApiKey, config.XunfeiApiSecret)
	if err != nil {
		return nil, nil, err
	}
	wsUrl := strings.Replace(authUrl, "https://", "wss://", 1)
	c, _, err := websocket.DefaultDialer.Dial(wsUrl, nil)
	if err != nil {
		return nil, nil, err
	}

	errorC := make(chan error)
	outputC := make(chan []byte)

	go func() {

		defer func() {
			c.Close()
			close(outputC)
			close(errorC)
		}()

		for {
			_, message, err := c.ReadMessage()
			if err == io.EOF || websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseAbnormalClosure) {
				return
			}

			if err != nil {
				errorC <- err
				return
			}

			var resp ResponseData
			err = json.Unmarshal(message, &resp)
			if err != nil {
				errorC <- err
				return
			}

			if resp.Code != 0 {
				errorC <- fmt.Errorf("xunfei get audio failed, code: %d, message: %s", resp.Code, resp.Message)
				return
			}

			if resp.Data.Audio != "" {
				audio, err := base64.StdEncoding.DecodeString(resp.Data.Audio)
				if err != nil {
					errorC <- err
					log.Infof(ctx, "[%s] get audio failed: %s", request.CallId, err.Error())
					return
				}

				outputC <- audio
				if resp.Data.Status == 2 {
					log.Infof(ctx, "[%s] get audio end", request.CallId)
					break
				}
			}
		}

	}()

	common := map[string]interface{}{
		"app_id": config.XunfeiAppID,
	}
	business := map[string]interface{}{
		"aue":    "raw",
		"auf":    "audio/L16;rate=8000",
		"tte":    "UTF8",
		"vcn":    params.ConvertSpeaker(request.Speaker, provider),
		"speed":  request.ConvertSpeed(provider),
		"volume": request.ConvertVolume(provider),
	}
	data := map[string]interface{}{
		"status": 2,
		"text":   params.GetTtsSSML(request, provider),
	}
	frame := map[string]interface{}{
		"common":   common,
		"business": business,
		"data":     data,
	}

	frameJson, err := json.Marshal(frame)
	if err != nil {
		return nil, nil, err
	}

	err = c.WriteMessage(websocket.TextMessage, frameJson)
	if err != nil {
		return nil, nil, err
	}

	return outputC, errorC, nil
}

func StreamAdapter(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, inPipe io.WriteCloser) error {
	outputC, errorC, err := GetStreamResponse(ctx, request, ttsParams)
	if err != nil {
		log.Infof(ctx, "[%s] get response failed: %s", request.TTSType, err.Error())
		return err
	}

	for {

		select {
		case content, ok := <-outputC:
			if !ok {
				return nil
			}
			_, err = inPipe.Write(content)
			if err != nil {
				log.Infof(ctx, "[%s] write to pipe failed: %s", request.CallId, err.Error())
				return err
			}
		case err := <-errorC:
			return err
		}
	}
}
