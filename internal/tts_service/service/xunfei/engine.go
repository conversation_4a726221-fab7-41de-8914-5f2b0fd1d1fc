package xunfei

import (
	"context"
	"tts-service/internal/pkg/code"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
	"tts-service/pkg/pck2wav"
)

type Engine struct {
	code    int
	err     error
	content []byte
}

func (engine *Engine) GetCode() int {
	return engine.code
}

func (engine *Engine) GetStatus() string {
	return engine.err.Error()
}

func (engine *Engine) GetAudio() *models.Wav {
	return &models.Wav{Content: engine.content}
}

func (engine *Engine) GenerateAudio(ctx context.Context, request *models.TTSRequest) bool {
	ttsParams, err := params.NewTTSParams(request)
	if err != nil {
		engine.code = code.ErrorMissingParams
		engine.err = err
		return false
	}

	outputC, errorC, err := GetStreamResponse(ctx, request, ttsParams)
	if err != nil {
		engine.code = code.ErrorRequestInternal
		engine.err = err
		return false
	}

	data := make([]byte, 0)
all:
	for {
		select {
		case content, ok := <-outputC:
			if content == nil && !ok {
				break all
			}
			data = append(data, content...)
		case err2 := <-errorC:
			err = err2
			engine.code = code.ErrorRequestInternal
			engine.err = err2
			break all
		}
	}

	if err != nil {
		engine.code = code.ErrorRequestInternal
		engine.err = err
		return false
	}

	engine.content, err = pck2wav.Pcm2Wav(data, 8000)
	if err != nil {
		engine.code = code.ErrorRequestInternal
		engine.err = err
		return false
	}

	return true
}
