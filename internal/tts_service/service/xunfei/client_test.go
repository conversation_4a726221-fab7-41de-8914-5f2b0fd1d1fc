package xunfei

import (
	"context"
	"fmt"
	"os"
	"testing"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/constants"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/convert"
	"tts-service/internal/tts_service/service/params"
	"tts-service/pkg/pck2wav"
)

const (
	//appid     = "ca58e069"
	//apiSecret = "MGU2MDU3NjkzZmI3MDUzNWYzZTZiNjEw"
	//apiKey    = "0ff4520b44faae93cba672b39c776ac1"

	appid     = "99aec895"
	apiSecret = "402616161a7c13db4bb76b7ed40271a0"
	apiKey    = "ZWY4MjI2MjQ4ODZiMjAxODhlNzI2MTJk"
	host      = "https://tts-api.xfyun.cn/v2/tts"

	//appid     = "ga2990a0"
	//apiSecret = "6afe6d1aed7965c1de0fc84f29f37fb2"
	//apiKey    = "aec3af3106d1d898b7276baf5324c8a0"
	//host      = "https://tts-api-sg.xf-yun.com/v2/tts"
)

func TestStreamAudio(t *testing.T) {
	log.InitLogger()
	config.XunfeiHost = host
	config.XunfeiAppID = appid
	config.XunfeiApiSecret = apiSecret
	config.XunfeiApiKey = apiKey
	request := &models.TTSRequest{
		TTSType: constants.XunfeiProvider,
		//Speaker: "x4_lingfeichen_assist",
		Speaker: "xiaoyan",
	}

	ttsParams := &params.TtsParams{
		Input: &params.InputParams{
			//SSML: "好的，为了帮助您冻结信用卡，我们需要先核验您的身份。请问您的N I R C号码是？\n好的，您的全名是？\n感谢您的配合，系统显示您名下一共有两张信用卡，一张主卡尾号是1122，一张副卡尾号是3 3 4 4，请问是哪张卡需要挂失呢？\n好的，现在为您转接人工处理。",
			SSML: "你好啊",
		},
	}
	c := convert.EmptyAdapter(context.Background(), request, ttsParams, StreamAdapter)
	audioContent := make([]byte, 0)
	for response := range c {
		if response.Response != nil {
			fmt.Printf("after: %f, content=%d\n", response.Response.DelayTime, len(response.Response.AudioContent))
			audioContent = append(audioContent, response.Response.AudioContent...)
		} else {
			fmt.Println(response.Err)
		}
	}

	z, _ := pck2wav.Pcm2Wav(audioContent, 8000)
	file, err := os.OpenFile("/Users/<USER>/zb/tts_test_16_8k_sg.wav", os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		fmt.Println(err, "open file error")
	}
	defer file.Close()
	_, _ = file.Write(z)

	_ = file.Sync()
}
