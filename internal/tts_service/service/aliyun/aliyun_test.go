package aliyun

import (
	"context"
	"fmt"
	"os"
	"testing"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
	"tts-service/pkg/pck2wav"
)

func TestStreamAudio(t *testing.T) {
	log.InitLogger()
	config.AliyunAccessKeyId = "LTAI5t85LoVomREBWJ9NUepW"
	config.AliyunAccessKeySecret = "******************************"
	config.AliyunAddress = "wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1"
	config.AliyunAppKey = "FxUrgq5R9VL9xELP"

	request := &models.TTSRequest{
		Speaker: "yue-CN-kelly",
	}

	ttsParams := &params.TtsParams{
		Input: &params.InputParams{
			SSML: "我哋诊所嘅应诊时间系星期一至星期五，上昼九点至下昼一点，下昼两点至四点。请问仲有咩可以帮到你？",
		},
	}

	content := make([]byte, 0)
	for audio := range StreamAdapter(context.Background(), request, ttsParams) {
		if audio.Err != nil {
			t.Errorf("StreamAdapter error: %v", audio.Err)
			return
		}

		content = append(content, audio.Response.AudioContent...)
	}

	z, _ := pck2wav.Pcm2Wav(content, 8000)
	file, err := os.OpenFile("/Users/<USER>/Desktop/1.wav", os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		fmt.Println(err, "open file error")
	}

	defer file.Close()
	_, _ = file.Write(z)

	_ = file.Sync()
}
