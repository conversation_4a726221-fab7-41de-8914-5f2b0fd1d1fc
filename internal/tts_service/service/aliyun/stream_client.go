package aliyun

import (
	"context"
	"fmt"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/auth/credentials"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/goccy/go-json"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/pkg/errors"
	"io"
	"strings"
	"time"
	"tts-service/internal/pkg/code"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/constants"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/convert"
	"tts-service/internal/tts_service/service/params"
)

const (
	MillSize  = 16
	ChunkSize = MillSize * 200
)

// Header 定义消息头结构
type Header struct {
	AppKey    string `json:"appkey"`
	MessageID string `json:"message_id"`
	Name      string `json:"name"`
	TaskID    string `json:"task_id"`
	Namespace string `json:"namespace"`
}

// Payload 定义合成参数结构
type Payload struct {
	Voice          string `json:"voice,omitempty"`
	Format         string `json:"format,omitempty"`
	SampleRate     int    `json:"sample_rate,omitempty"`
	Volume         int    `json:"volume,omitempty"`
	SpeechRate     int    `json:"speech_rate,omitempty"`
	PitchRate      int    `json:"pitch_rate,omitempty"`
	EnableSubtitle bool   `json:"enable_subtitle,omitempty"`
	Platform       string `json:"platform,omitempty"`
	Text           string `json:"text,omitempty"`
}

type MessageResponse struct {
	Header struct {
		MessageId     string `json:"message_id"`
		TaskId        string `json:"task_id"`
		Namespace     string `json:"namespace"`
		Name          string `json:"name"`
		Status        int    `json:"status"`
		StatusMessage string `json:"status_message"`
	} `json:"header"`
}

type AliyunResult struct {
	Audio []byte
	Err   error
}

type TokenResult struct {
	ErrMsg string
	Token  struct {
		UserId     string
		Id         string
		ExpireTime int64
	}
}

// Message 定义完整的消息结构
type Message struct {
	Header  Header  `json:"header"`
	Payload Payload `json:"payload"`
}

func generateUUID() string {
	return strings.ReplaceAll(uuid.New().String(), "-", "")
}

func getHeader(callId string, name string) map[string]string {
	return map[string]string{
		"appkey":     config.AliyunAppKey,
		"message_id": generateUUID(),
		"task_id":    callId,
		"namespace":  config.AliyunNamespace,
		"name":       name,
	}
}

func GetAliyunToken() (string, error) {
	cre := credentials.NewStaticAKCredentialsProviderBuilder()
	provider, _ := cre.
		WithAccessKeyId(config.AliyunAccessKeyId).
		WithAccessKeySecret(config.AliyunAccessKeySecret).
		Build()

	client, err := sdk.NewClientWithOptions(config.AliyunTokenRegion, sdk.NewConfig(), provider)

	if err != nil {
		return "", errors.Wrap(err, "aliyun NewClientWithOptions error")
	}

	request := requests.NewCommonRequest()
	request.Method = "POST"
	request.Domain = config.AliyunTokenDomain
	request.ApiName = config.AliyunTokenAction
	request.Version = config.AliyunTokenVersion
	response, err := client.ProcessCommonRequest(request)
	if err != nil {
		return "", errors.Wrap(err, "aliyun ProcessCommonRequest error")
	}

	var tr TokenResult
	err = json.Unmarshal([]byte(response.GetHttpContentString()), &tr)
	if err != nil {
		return "", errors.Wrap(err, "aliyun Unmarshal error")
	}

	return tr.Token.Id, nil
}

func GetStartMessage(taskId string, request *models.TTSRequest, params *params.TtsParams) map[string]interface{} {
	return map[string]interface{}{
		"header": getHeader(taskId, "StartSynthesis"),
		"payload": map[string]interface{}{
			"voice":       params.ConvertSpeaker(request.Speaker, constants.AliyunProvider),
			"format":      "pcm",
			"sample_rate": 8000,
		},
	}
}

func GetRunMessage(taskId string, request *models.TTSRequest, params *params.TtsParams) map[string]interface{} {
	return map[string]interface{}{
		"header": getHeader(taskId, "RunSynthesis"),
		"payload": map[string]interface{}{
			"text": params.GetTtsSSML(request, constants.AliyunProvider),
		},
	}
}

func GetStopMessage(taskId string, request *models.TTSRequest, params *params.TtsParams) map[string]interface{} {
	return map[string]interface{}{
		"header": getHeader(taskId, "StopSynthesis"),
	}
}

func GetStreamResponse(ctx context.Context, request *models.TTSRequest, params *params.TtsParams) (chan AliyunResult, error) {
	token, err := GetAliyunToken()
	if err != nil {
		return nil, errors.Wrap(err, "aliyun get token error")
	}

	url := fmt.Sprintf("%s?token=%s", config.AliyunAddress, token)
	c, _, err := websocket.DefaultDialer.Dial(url, nil)
	if err != nil {
		return nil, errors.Wrap(err, "aliyun websocket dial error")
	}
	audioC := make(chan AliyunResult, 10)

	taskId := generateUUID()
	go func() {
		defer func() {
			close(audioC)
			_ = c.Close()
		}()

		err = c.WriteJSON(GetStartMessage(taskId, request, params))
		if err != nil {
			audioC <- AliyunResult{
				Err: errors.Wrap(err, "aliyun write start message error"),
			}
			return
		}

		for {
			messageType, message, err := c.ReadMessage()
			if err == io.EOF {
				return
			}

			if err != nil {
				audioC <- AliyunResult{
					Err: errors.Wrap(err, "aliyun read message error"),
				}
				return
			}

			if messageType == websocket.TextMessage {
				log.Infof(ctx, "receive aliyun message: %s", string(message))
				var response MessageResponse
				err = json.Unmarshal(message, &response)
				if err != nil {
					audioC <- AliyunResult{
						Err: errors.Wrap(err, "aliyun unmarshal message error"),
					}
					return
				}
				if response.Header.Name == "SynthesisCompleted" {
					return
				} else if response.Header.Name == "SynthesisStarted" {
					err = c.WriteJSON(GetRunMessage(taskId, request, params))
					if err != nil {
						audioC <- AliyunResult{
							Err: errors.Wrap(err, "aliyun write run message error"),
						}
						return
					}

					err = c.WriteJSON(GetStopMessage(taskId, request, params))
					if err != nil {
						audioC <- AliyunResult{
							Err: errors.Wrap(err, "aliyun write stop message error"),
						}
						return
					}
				}
			} else if messageType == websocket.BinaryMessage {
				audioC <- AliyunResult{
					Audio: message,
				}
			} else if messageType == websocket.CloseMessage {
				return
			}
		}
	}()

	return audioC, nil
}

func StreamAdapter(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams) chan *convert.StreamAudio {
	responseC := make(chan *convert.StreamAudio, 10)
	audioC, err := GetStreamResponse(ctx, request, ttsParams)
	if err != nil {
		responseC <- &convert.StreamAudio{
			Err:       err,
			ErrorCode: code.ErrorInternal,
		}
		return responseC
	}

	startTime := time.Now()
	go func() {
		defer close(responseC)
		audioContent := make([]byte, 0, ChunkSize*10)
		for audio := range audioC {
			if audio.Err != nil {
				responseC <- &convert.StreamAudio{
					Err:       audio.Err,
					ErrorCode: code.ErrorInternal,
				}
				return
			}

			audioContent = append(audioContent, audio.Audio...)
			if len(audioContent) >= ChunkSize {
				responseC <- &convert.StreamAudio{
					Response: &params.ClientResponse{
						AudioContent: audioContent[:ChunkSize],
						Code:         code.ServerOK,
						Duration:     200 * 1000,
						DelayTime:    float64(time.Now().Sub(startTime).Milliseconds()),
					},
				}
				startTime = time.Now()
				audioContent = audioContent[ChunkSize:]
			}
		}

		responseC <- &convert.StreamAudio{
			Response: &params.ClientResponse{
				AudioContent: audioContent,
				Code:         code.ServerOK,
				IsEnd:        true,
				Duration:     int64(len(audioContent)/MillSize) * 1000,
				DelayTime:    float64(time.Now().Sub(startTime).Milliseconds()),
			},
		}
	}()

	return responseC
}
