package aliyun

import (
	"context"
	"go.uber.org/zap/buffer"
	"tts-service/internal/pkg/code"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
	"tts-service/pkg/pck2wav"
)

type Engine struct {
	code    int
	err     error
	content []byte
}

func (engine *Engine) GetCode() int {
	return engine.code
}

func (engine *Engine) GetStatus() string {
	return engine.err.Error()
}

func (engine *Engine) GetAudio() *models.Wav {
	return &models.Wav{Content: engine.content}
}

func (engine *Engine) GenerateAudio(ctx context.Context, request *models.TTSRequest) bool {
	ttsParams, err := params.NewTTSParams(request)
	if err != nil {
		engine.code = code.ErrorMissingParams
		engine.err = err
		return false
	}

	audioC, err := GetStreamResponse(ctx, request, ttsParams)
	if err != nil {
		engine.code = code.ErrorRequestInternal
		engine.err = err
		return false
	}

	var audioContent buffer.Buffer
	for audio := range audioC {
		if audio.Err != nil {
			engine.code = code.ErrorRequestInternal
			engine.err = audio.Err
			return false
		}
		if audio.Audio != nil {
			_, _ = audioContent.Write(audio.Audio)
		}
	}

	engine.content, _ = pck2wav.Pcm2Wav(audioContent.Bytes(), 8000)
	return true
}
