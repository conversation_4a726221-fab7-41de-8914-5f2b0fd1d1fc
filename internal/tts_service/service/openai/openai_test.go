package openai

import (
	"context"
	"fmt"
	"os"
	"testing"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/convert"
	"tts-service/internal/tts_service/service/params"
	"tts-service/pkg/pck2wav"
)

func TestStreamAudio(t *testing.T) {
	log.InitLogger()
	config.OpenAiAuthKey = "Bearer ***************************************************"
	config.OpenAiModel = "tts-1-hd"

	request := &models.TTSRequest{
		Speaker: "alloy",
	}

	ttsParams := &params.TtsParams{
		Input: &params.InputParams{
			SSML: "Hey guys, if you're ever in the Big Apple, come check out my pad in the heart of Manhattan! My address is 123 Main Street, New York, NY. It's just a short walk from Times Square and has all the best restaurants and shops nearby.",
		},
	}
	c := convert.PcmConvertAdapter(context.Background(), request, ttsParams, StreamAdapter)
	audioContent := make([]byte, 0)
	for response := range c {
		if response.Response != nil {
			fmt.Printf("after: %f, content=%d\n", response.Response.DelayTime, len(response.Response.AudioContent))
			audioContent = append(audioContent, response.Response.AudioContent...)
		} else {
			fmt.Println(response.Err)
		}
	}

	z, _ := pck2wav.Pcm2Wav(audioContent, 8000)
	file, _ := os.OpenFile("/Users/<USER>/Desktop/地址_hd.wav", os.O_WRONLY|os.O_CREATE, 0666)

	defer file.Close()
	_, _ = file.Write(z)

	_ = file.Sync()
}
