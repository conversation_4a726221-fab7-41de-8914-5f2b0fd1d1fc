package minimax

import (
	"context"
	"io"
	"tts-service/internal/pkg/code"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
	"tts-service/pkg/pck2wav"
)

type Engine struct {
	code    int
	err     error
	content []byte
}

func (engine *Engine) GetCode() int {
	return engine.code
}

func (engine *Engine) GetStatus() string {
	return engine.err.Error()
}

func (engine *Engine) GetAudio() *models.Wav {
	return &models.Wav{Content: engine.content}
}

func (engine *Engine) GenerateAudio(ctx context.Context, request *models.TTSRequest) bool {

	var (
		content []byte
	)
	ttsParams, err := params.NewTTSParams(request)
	if err != nil {
		engine.code = code.ErrorMissingParams
		engine.err = err
		return false
	}
	errChan := make(chan error, 1)
	outPipe, inPipe := io.Pipe()
	go func() {
		defer inPipe.Close()
		errChan <- StreamAdapter(ctx, request, ttsParams, inPipe)
	}()

	line := make([]byte, 16*200)
	for {
		n, err := outPipe.Read(line)
		if err == io.EOF {
			//eof也要尝试追加数据
			if n > 0 {
				content = append(content, line[:n]...)
			}
			break
		}
		if err != nil {
			engine.code = code.ErrorRequestInternal
			engine.err = err
			return false
		}
		content = append(content, line[:n]...)
	}

	if err := <-errChan; err != nil {
		engine.code = code.ErrorRequestInternal
		engine.err = err
		return false
	}

	engine.content, err = pck2wav.Pcm2Wav(content, 8000)
	if err != nil {
		engine.code = code.ErrorInternal
		engine.err = err
		return false
	}
	return true
}
