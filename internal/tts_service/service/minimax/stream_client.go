package minimax

import (
	"bufio"
	"bytes"
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"io"
	"net/http"
	"time"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/constants"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
)

const (
	groupID = "1881949950087020600"
	apiKey  = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	//groupID    = "1882281116061143098"
	//apiKey     = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	baseURL    = "https://api.minimax.chat/v1/t2a_v2"
	fileFormat = "pcm"
)

// VoiceSetting represents the voice configuration
type VoiceSetting struct {
	VoiceID string  `json:"voice_id"`
	Speed   float64 `json:"speed"`
	Vol     float64 `json:"vol"`
	Pitch   int     `json:"pitch"`
	Emotion *string `json:"emotion,omitempty"`
}

// AudioSetting represents the audio configuration
type AudioSetting struct {
	SampleRate int    `json:"sample_rate"`
	Bitrate    int    `json:"bitrate"`
	Format     string `json:"format"`
	Channel    int    `json:"channel"`
}

// TTSRequestBody represents the request body for TTS API
type TTSRequestBody struct {
	Model         string       `json:"model"`
	Text          string       `json:"text"`
	Stream        bool         `json:"stream"`
	LanguageBoost *string      `json:"language_boost,omitempty"`
	VoiceSetting  VoiceSetting `json:"voice_setting"`
	AudioSetting  AudioSetting `json:"audio_setting"`
}

// TTSResponse represents the response from TTS API
type TTSResponse struct {
	Data struct {
		Audio  string `json:"audio"`
		Status int    `json:"status"`
	} `json:"data"`
	BaseResp RespStatus `json:"base_resp"`
}

type RespStatus struct {
	StatusCode int    `json:"status_code"`
	StatusMsg  string `json:"status_msg"`
}

func buildTTSStreamHeaders() map[string]string {
	return map[string]string{
		"accept":        "application/json, text/plain, */*",
		"content-type":  "application/json",
		"authorization": "Bearer " + config.MiniMaxApiKey,
	}
}

func buildTTSStreamBody(request *models.TTSRequest, param *params.TtsParams) TTSRequestBody {
	body := TTSRequestBody{
		Model:  param.GetModel(),
		Text:   param.GetTtsSSML(request, constants.MinimaxProvider),
		Stream: true,
		VoiceSetting: VoiceSetting{
			VoiceID: param.ConvertSpeaker(request.Speaker, constants.MinimaxProvider),
			Speed:   param.GetSpeed(),
			Vol:     param.GetVolume(constants.MinimaxProvider),
			Pitch:   param.GetPitch(),
			Emotion: param.GetEmotion(),
		},
		AudioSetting: AudioSetting{
			SampleRate: 8000,
			Bitrate:    128000,
			Format:     fileFormat,
			Channel:    1,
		},
	}

	if boost := param.GetLanguageBoost(); boost != nil {
		body.LanguageBoost = boost
	}
	return body
}

func GetStreamResponse(ctx context.Context, request *models.TTSRequest, params *params.TtsParams) (io.ReadCloser, error) {

	reqUrl := config.MiniMaxHost + "?GroupId=" + config.MiniMaxGroupID
	headers := buildTTSStreamHeaders()
	body := buildTTSStreamBody(request, params)
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request body: %v", err)
	}

	log.Infof(
		ctx,
		"[minimax__v1] start http request:%s, body: %v",
		request.TTSType,
		reqUrl,
		string(jsonBody),
	)
	timeoutCtx, _ := context.WithTimeout(ctx, 60*time.Second)
	req, err := http.NewRequestWithContext(timeoutCtx, "POST", reqUrl, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		fmt.Printf("Error making request: %v\n", err)
		log.Errorf(ctx, "Error making request: %v", err)
		return nil, err
	}

	if resp.StatusCode != 200 {
		return nil, errors.Errorf("minimax response status code is %d", resp.StatusCode)
	}
	return resp.Body, nil
}

func StreamAdapter(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, inPipe io.WriteCloser) error {
	body, err := GetStreamResponse(ctx, request, ttsParams)
	if err != nil {
		log.Infof(ctx, "[%s] get response failed: %s", request.TTSType, err.Error())
		return err
	}

	defer body.Close()
	reader := bufio.NewReader(body)
	for {

		/**
		 * 海螺可能返回
		 * 1. data: {"base_resp":{"status_code":20132,"status_msg":"code: 20132, msg: invalid samples or voice_id"}}
		 * 2. 不带前缀的包 {"base_resp":{"status_code":1008,"status_msg":""}}
		 * 3  空包 data: {"data":{"audio":"","status":1,"ced":""},"trace_id":"03e28d93c72b4a33cbf524215c380d1c","base_resp":{"status_code":0,"status_msg":""}}
		 */

		line, err := reader.ReadBytes('\n')
		if err != nil && err != io.EOF {
			fmt.Printf("Error reading response: %v\n", err)
			return err
		}

		data := line
		if bytes.HasPrefix(line, []byte("data:")) {
			data = line[5:]
		}

		var response TTSResponse
		if err := json.Unmarshal(data, &response); err != nil {
			continue
		}

		if response.Data.Status == 2 {
			break
		}

		if response.BaseResp.StatusCode != 0 {
			fmt.Printf("minimax get audio failed, code: %d, message: %s\n", response.BaseResp.StatusCode, response.BaseResp.StatusMsg)
			log.Errorf(ctx, "minimax get audio failed, code: %d, message: %s", response.BaseResp.StatusCode, response.BaseResp.StatusMsg)
			return errors.Errorf("minimax get audio failed, code: %d, message: %s", response.BaseResp.StatusCode, response.BaseResp.StatusMsg)
		}

		if response.Data.Audio != "" {
			audioBytes, err := hex.DecodeString(response.Data.Audio)
			if err != nil {
				fmt.Printf("Error decoding audio: %v\n", err)
				log.Errorf(ctx, "Error decoding audio: %v", err)
				return err

			}
			_, _ = inPipe.Write(audioBytes)
		}

		if err == io.EOF {
			fmt.Printf("minimax callTTSStream EOF\n")
			break
		}
	}
	return nil
}
