package minimax

import (
	"context"
	"fmt"
	"os"
	"testing"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/constants"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/convert"
	"tts-service/internal/tts_service/service/params"
	"tts-service/pkg/pck2wav"
)

const (
	//个人
	//group      = "1881949950087020600"
	//testapiKey = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	host = "https://api.minimax.chat/v1/t2a_v2"

	//公司
	group      = "1882281116061143098"
	testapiKey = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

	//海外
	//group      = "1881173159365841087"
	//testapiKey = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************.IlZv9jwfcThArttMuzKWrbjtjPleZdv7kzvcs81OBrzA4Ajl5bryKJmU9Jgems6fzIx0u5B4tazZdPOZnhcPM_tY53ktfYzZ9fxpPXuqvoOb_L68DMZnJgbb80MG97P_dyNcuqRP1RnjyoBxIGlp8wOn-EqmU4QIX7Ra3Hb0jKAIriILte6xKNjJ6Ve_dID4Tr1wdysP8dWu2t-iGFNXfwHYkHAbR2PCtrlZjwVagEqt_A6bTWote5NaZjT8neu5l9HNmZaEKK13fM1E73p7XFxLIf-0oVoYG_oik9lxerggVow_Y9HeBHGfmJl2SrFajlTMv3Tmx7aqe3M5tuoVBQ"
	//host       = "https://api.minimaxi.chat/v1/t2a_v2"
)

func TestStreamAudio(t *testing.T) {
	log.InitLogger()
	config.MiniMaxHost = host
	config.MiniMaxGroupID = group
	config.MiniMaxApiKey = testapiKey
	config.MinimaxVoiceMaps = map[string]string{
		"Myriam": "Arrogant_Miss",
	}
	request := &models.TTSRequest{
		Version:     "",
		TTSType:     constants.MinimaxProvider,
		Speaker:     "Arrogant_Miss",
		Locale:      "",
		Text:        "",
		AccountType: "",
		Stream:      true,
	}

	ttsParams := &params.TtsParams{
		LLMParameter: &params.LLMParameter{Model: "speech-01-turbo"},
		Input: &params.InputParams{
			//SSML: "好的，为了帮助您冻结信用卡，我们需要先核验您的身份。请问您的N I R C号码是？\n好的，您的全名是？\n感谢您的配合，系统显示您名下一共有两张信用卡，一张主卡尾号是1122，一张副卡尾号是3 3 4 4，请问是哪张卡需要挂失呢？\n好的，现在为您转接人工处理。",
			SSML: "你好，欢迎你",
		},
	}
	c := convert.EmptyAdapter(context.Background(), request, ttsParams, StreamAdapter)
	audioContent := make([]byte, 0)
	for response := range c {
		if response.Response != nil {
			fmt.Printf("after: %f, content=%d\n", response.Response.DelayTime, len(response.Response.AudioContent))
			audioContent = append(audioContent, response.Response.AudioContent...)
		} else {
			fmt.Printf("response nil, response code:%d err:%s", response.ErrorCode, response.Err.Error())
		}
	}

	z, _ := pck2wav.Pcm2Wav(audioContent, 8000)
	file, err := os.OpenFile("/Users/<USER>/zb/minimax_tts_test_16_8k_hello1.wav", os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		fmt.Println(err, "open file error")
	}
	defer file.Close()
	_, _ = file.Write(z)

	_ = file.Sync()
}

func TestGenerateAudio(t *testing.T) {
	log.InitLogger()
	config.MiniMaxHost = host
	config.MiniMaxGroupID = group
	config.MiniMaxApiKey = testapiKey
	config.MinimaxVoiceMaps = map[string]string{
		"Myriam": "Arrogant_Miss",
	}
	engine := &Engine{}

	request := &models.TTSRequest{
		Version:      "",
		TTSType:      "minimax__v1",
		Speaker:      "zh-CN-Myriam",
		Locale:       "zh-CN",
		Text:         "你好，欢迎你",
		AccountType:  "ttsdemo",
		SpeakingRate: 1,
	}

	success := engine.GenerateAudio(context.Background(), request)
	if success {
		fmt.Println("success")
		//engine.content 保存为wav文件
		file, err := os.OpenFile("/Users/<USER>/zb/minimax_tts_test_16_8k_fix2.wav", os.O_WRONLY|os.O_CREATE, 0666)
		if err != nil {
			fmt.Println(err, "open file error")
		}
		defer file.Close()
		_, _ = file.Write(engine.content)

		_ = file.Sync()

	} else {
		fmt.Println("failed")
	}
}
