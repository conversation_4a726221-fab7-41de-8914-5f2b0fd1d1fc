package service

import (
	"context"
	"fmt"
	"github.com/hashicorp/go-uuid"
	"github.com/pkg/errors"
	"os"
	"tts-service/internal/pkg/code"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
)

func GenerateTTS(ctx context.Context, provider string, request *models.TTSRequest) (*models.ErrorResponse, *models.Wav) {
	if request.IsEmpty() {
		errMsg := "no tts_type and speaker is empty (音色为空)"
		log.Error(ctx, errMsg)
		return models.NewErrorResponse(code.ErrorMissingParams, errMsg), nil
	}

	// 根据 provider 获取指定的引擎信息
	engine := GetEngineByProvider(provider)
	if engine.GenerateAudio(ctx, request) {
		return nil, engine.GetAudio()
	} else {
		return models.NewErrorResponse(engine.GetCode(), engine.GetStatus()), nil
	}
}

// WriteToFile 将音频内容写入到文件里面
func WriteToFile(_ context.Context, request *models.TTSRequest, content []byte) (string, error) {
	key, err := uuid.GenerateUUID()
	if err != nil {
		return "", errors.Wrap(err, "generate uuid error.")
	}

	filename := fmt.Sprintf("audio/%s_%s_%s.wav", request.Locale, request.Speaker, key)

	file, err := os.OpenFile(filename, os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		return "", errors.Wrap(err, "open file error")
	}

	defer func(file *os.File) {
		_ = file.Close()
	}(file)
	_, _ = file.Write(content)

	_ = file.Sync()
	return filename, nil
}
