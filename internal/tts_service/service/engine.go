package service

import (
	"context"
	"tts-service/internal/pkg/constants"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/airudder_v2"
	"tts-service/internal/tts_service/service/aliyun"
	"tts-service/internal/tts_service/service/deepgram"
	"tts-service/internal/tts_service/service/elevenlabs"
	"tts-service/internal/tts_service/service/google"
	"tts-service/internal/tts_service/service/microsoft"
	"tts-service/internal/tts_service/service/minimax"
	"tts-service/internal/tts_service/service/openai"
	"tts-service/internal/tts_service/service/xunfei"
)

type TTSEngine interface {
	// GetCode 返回 TTS 执行结果 code
	GetCode() int
	// GetStatus 返回 TTS 执行结果描述
	GetStatus() string
	// GetAudio 返回 TTS 执行结果音频数据
	GetAudio() *models.Wav
	// GenerateAudio 执行 TTS 引擎，返回是否执行成功
	GenerateAudio(ctx context.Context, request *models.TTSRequest) bool
}

func GetEngineByProvider(provider string) TTSEngine {
	switch provider {
	case constants.AirudderV2Provider:
		return &airudder_v2.Engine{}
	case constants.GoogleProvider:
		return &google.Engine{}
	case constants.MicrosoftProvider:
		return &microsoft.Engine{}
	case constants.ElevenLabsProvider:
		return &elevenlabs.Engine{}
	case constants.OpenAiProvider:
		return &openai.Engine{}
	case constants.XunfeiProvider:
		return &xunfei.Engine{}
	case constants.DeepgramProvider:
		return &deepgram.Engine{}
	case constants.AliyunProvider:
		return &aliyun.Engine{}
	case constants.MinimaxProvider:
		return &minimax.Engine{}
	}

	return nil
}
