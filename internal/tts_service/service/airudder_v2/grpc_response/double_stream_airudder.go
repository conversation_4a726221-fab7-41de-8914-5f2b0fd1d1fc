package grpc_response

import (
	"tts-service/internal/pkg/code"
	"tts-service/internal/tts_service/service/params"
	"tts-service/rpc/double_stream_tts/double_stream"
)

type DoubleStreamAirudderResponse struct {
	Future *double_stream.TtsResponse
}

func (response *DoubleStreamAirudderResponse) GetClientResponse() *params.ClientResponse {
	responseCode := response.Future.Code
	if responseCode == 0 {
		responseCode = code.ServerOK
	}

	duration := int64(0)
	if chunkMeta := response.Future.GetTtsResponseChunkMeta(); chunkMeta != nil {
		duration = chunkMeta.GetChunkSizeUs()
	}

	clientResponse := &params.ClientResponse{
		AudioContent: response.Future.AudioContent,
		Status:       response.Future.Status,
		Code:         int(responseCode),
		Duration:     duration,
		IsEnd:        response.Future.IsEnd,
	}

	return clientResponse
}

func (response *DoubleStreamAirudderResponse) GetRtf() float64 {
	if response.Future.TtsResponseChunkMeta != nil {
		return float64(response.Future.TtsResponseChunkMeta.Rtf)
	}
	return 0
}

func (response *DoubleStreamAirudderResponse) GetIsEnd() bool {
	return response.Future.IsEnd
}
