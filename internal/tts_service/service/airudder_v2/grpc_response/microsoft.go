package grpc_response

import (
	"tts-service/internal/pkg/code"
	"tts-service/internal/tts_service/service/params"
	"tts-service/rpc/microsoft/pb"
)

type MicrosoftResponse struct {
	Future *pb.MicrosoftTtsResponse
}

func (response *MicrosoftResponse) GetClientResponse() *params.ClientResponse {
	var responseCode int
	switch response.Future.Code {
	case 2:
		responseCode = code.ErrorSpeak
	case 3, 1013:
		responseCode = code.ErrorQuota
	case 6:
		responseCode = code.ErrorTimeout
	case code.ServerOK:
		responseCode = code.ServerOK
	default:
		responseCode = code.ErrorInternal
	}

	return &params.ClientResponse{
		AudioContent: response.Future.AudioContent,
		Status:       response.Future.Status,
		Code:         responseCode,
		Duration:     response.Future.Duration,
		IsEnd:        response.Future.IsEnd,
	}
}

func (response *MicrosoftResponse) GetRtf() float64 {
	return response.Future.Rtf
}

func (response *MicrosoftResponse) GetIsEnd() bool {
	return response.Future.IsEnd
}
