package grpc_response

import (
	"tts-service/internal/pkg/code"
	"tts-service/internal/tts_service/service/params"
	"tts-service/rpc/tts/pb"
)

type AirudderResponse struct {
	Future *pb.TtsResponse
}

func (response *AirudderResponse) GetClientResponse() *params.ClientResponse {
	responseCode := response.Future.Code
	if responseCode == 0 {
		responseCode = code.ServerOK
	}

	duration := int64(0)
	if chunkMeta := response.Future.GetTtsResponseChunkMeta(); chunkMeta != nil {
		duration = chunkMeta.GetChunkSizeUs()
	}

	clientResponse := &params.ClientResponse{
		AudioContent: response.Future.AudioContent,
		Status:       response.Future.Status,
		Code:         int(responseCode),
		Duration:     duration,
		IsEnd:        response.Future.IsEnd,
	}

	if response.Future.TtsResponseMeta != nil {
		if results := response.Future.TtsResponseMeta.FrontendResults; results != nil {
			words := make([]params.WordResponse, 0, len(results.Words))
			for _, word := range results.Words {
				if word.Word == "sil" && len(word.Phonemes) == 1 && word.Phonemes[0] == "sil" {
					continue
				}

				if len(word.PhonemeEndUs) > 0 {
					words = append(words, params.WordResponse{
						Word:     word.Word,
						Duration: word.PhonemeEndUs[len(word.PhonemeEndUs)-1],
					})
				}
			}

			clientResponse.Words = words
		}
	}

	return clientResponse
}

func (response *AirudderResponse) GetRtf() float64 {
	if response.Future.TtsResponseChunkMeta != nil {
		return float64(response.Future.TtsResponseChunkMeta.Rtf)
	}

	return 0
}

func (response *AirudderResponse) GetIsEnd() bool {
	return response.Future.IsEnd
}
