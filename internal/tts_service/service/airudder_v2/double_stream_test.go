package airudder_v2

import (
	"context"
	"os"
	"testing"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
)

func TestAirudderDoubleStreamAudio(t *testing.T) {
	t.Log("TestAirudderDoubleStreamAudio")
	ctx := context.Background()
	request := &models.TTSRequest{}
	ttsParams := &params.TtsParams{}
	textChan := make(chan string, 0)
	audioContent := make([]byte, 0)
	for audio := range GenerateStreamAudio(ctx, request, ttsParams, textChan) {
		if audio.Err != nil {
			t.Fatalf("GenerateStreamAudio error: %v", audio.Err)
		}

		audioContent = append(audioContent, audio.Response.AudioContent...)
	}

	file, _ := os.OpenFile("/Users/<USER>/Desktop/audio_pcm_latency_0.wav", os.O_WRONLY|os.O_CREATE, 0666)

	defer file.Close()
	_, _ = file.Write(audioContent)

	_ = file.Sync()
}
