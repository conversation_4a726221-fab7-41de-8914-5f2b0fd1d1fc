package airudder_v2

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"
	"net/http"
	"strings"
	"sync"
	"time"
	"tts-service/internal/pkg/code"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/log"
	"tts-service/internal/pkg/metrics"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
)

const (
	ingressClientTimeOut = 20 * time.Second
)

var (
	ingressClient *IngressClient
	ingressOnce   sync.Once
)

type IngressClient struct {
	http *resty.Client
}

func GetIngressClient() *IngressClient {
	ingressOnce.Do(func() {
		ingressClient = &IngressClient{
			http: resty.New().SetTimeout(ingressClientTimeOut),
		}
	})
	return ingressClient
}

func (client *IngressClient) GetAudio(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, retryTimes int) (*params.ClientResponse, int, error) {
	url := fmt.Sprintf("%s/%s/v1/text:synthesis", config.Config.GetString("ingress_url"), request.ServerName)

	resp, err := client.http.R().
		SetHeader("Content-Type", "application/json; charset=UTF-8").
		SetBody(ttsParams).
		Post(url)

	errorCode := http.StatusOK
	if err != nil {
		errorCode = code.ErrorInternal
		if strings.Contains(err.Error(), "no healthy upstream") {
			errorCode = code.ErrorContainer
		}
	}

	// 这里返回的请求不是 contentType=json，所以 resty 框架无法正确 unmarshal 数据，所以手动 unmarshal
	var result params.ClientResponse
	if err == nil {
		err = json.Unmarshal(resp.Body(), &result)
		if err != nil {
			log.Errorf(ctx, "unmarshal content error: %s", err.Error())
			errorCode = code.ErrorInternal
			err = errors.Wrapf(err, "code=%d, body=%s", resp.StatusCode(), resp.Body())
		}
	}

	errorCode = result.Code
	if resp.StatusCode() == http.StatusServiceUnavailable || result.Code == code.ErrorContainer {
		errorCode = code.ErrorContainer
		err = errors.New(fmt.Sprintf("call ingress error, resp.code is %d", errorCode))
	} else if resp.StatusCode() == http.StatusNotFound || result.Code == code.ErrorRouter {
		errorCode = code.ErrorRouter
		err = errors.New(fmt.Sprintf("k8s gateway path /%s not exists! return none", request.ServerName))
	} else if resp.StatusCode() != http.StatusOK {
		errorCode = code.ErrorInternal
		err = errors.New(fmt.Sprintf("call airudder__v2 resp.code is %d", resp.StatusCode()))
	} else if len(result.AudioContent) == 0 && result.Code != 0 {
		errorCode = result.Code
		err = errors.New(result.Status)
	} else if len(result.AudioContent) == 0 {
		errorCode = code.ErrorInternal
		err = errors.New("airudder v2: audio_content is none, return none")
	}

	if retryTimes > 0 && code.CheckCodeNeedRepeated(errorCode) {
		time.Sleep(1 * time.Second)
		return client.GetAudio(ctx, request, ttsParams, retryTimes-1)
	}

	if resp.StatusCode() == http.StatusServiceUnavailable || result.Code == code.ErrorContainer {
		go GetK8sClient().pull(ctx, request.ServerName, "")
	}

	if err == nil && retryTimes != 2 && retryTimes >= 0 {
		log.Infof(ctx, "retry success times: %d", 2-retryTimes)
		metrics.RequestRetrySuccess.Inc()
	}
	return &result, errorCode, err
}
