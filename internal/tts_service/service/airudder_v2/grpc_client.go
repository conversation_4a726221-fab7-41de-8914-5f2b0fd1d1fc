package airudder_v2

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"google.golang.org/grpc/metadata"
	"io"
	"regexp"
	"strings"
	"time"
	"tts-service/internal/pkg/code"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/constants"
	"tts-service/internal/pkg/log"
	"tts-service/internal/pkg/metrics"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/airudder_v2/grpc_response"
	"tts-service/internal/tts_service/service/params"
	"tts-service/rpc/microsoft"
	microsoftTTS "tts-service/rpc/microsoft/pb"
	realtimeTTS "tts-service/rpc/service/pb"
	"tts-service/rpc/tts"
	"tts-service/rpc/tts/pb"
)

var ttsXMLPattern = regexp.MustCompile("<.*?>")

type GrpcClient struct {
}

func GetGrpcClient() *GrpcClient {
	return &GrpcClient{}
}

type AudioClientResponse struct {
	Response  *params.ClientResponse
	ErrorCode int
	Err       error
}

func NewAudioClientResponse(response *params.ClientResponse, errorCode int, err error) *AudioClientResponse {
	return &AudioClientResponse{
		Response:  response,
		ErrorCode: errorCode,
		Err:       err,
	}
}

func (grpcClient *GrpcClient) newTTSConfig(request *models.TTSRequest, params *params.TtsParams, stream bool) *pb.TtsConfig {
	return &pb.TtsConfig{
		RequestId: request.CallId,
		Voice: &pb.VoiceConfig{
			LanguageCode: params.Voice.LanguageCode,
			Name:         params.Voice.Name,
			SsmlGender:   params.Voice.SSMLGender,
		},
		Stream:       stream,
		AudioChunkUs: config.Config.GetInt64("stream.chunk_us"),
		Audio: &pb.AudioConfig{
			AudioEncoding:   params.AudioConfig.AudioEncoding,
			SpeakingRate:    float32(params.AudioConfig.SpeakingRate),
			VolumeGainDb:    float32(params.AudioConfig.VolumeGainDb),
			SampleRateHertz: int32(params.AudioConfig.SampleRateHertz),
		},
	}
}

func getDefaultClient(ctx context.Context, serverName string) (pb.TtsClient, context.Context) {
	client := tts.GetTTSClient(ctx)
	header := metadata.New(map[string]string{
		"tts-service-name": serverName,
	})

	ctx = metadata.NewOutgoingContext(ctx, header)

	return client, ctx
}

func checkSynthesisError(err error, ctx context.Context, request *models.TTSRequest) *AudioClientResponse {
	if strings.Contains(err.Error(), "no healthy upstream") || strings.Contains(err.Error(), "Unimplemented") {
		go GetK8sClient().pull(ctx, request.ServerName, request.AppName)
		return NewAudioClientResponse(nil, code.ErrorContainer, errors.Wrap(err, "grpc synthesis connect error"))
	} else if strings.Contains(err.Error(), "context canceled") {
		return NewAudioClientResponse(nil, code.ErrorContextCanceled, errors.Wrap(err, "grpc synthesis context canceled"))
	}
	return NewAudioClientResponse(nil, code.ErrorInternal, errors.Wrap(err, "grpc synthesis connect error"))
}

func handleSynthesisResponse(ctx context.Context, request *models.TTSRequest, future grpc_response.Response, err error, lastStartTime time.Time, lastIndex int) (*AudioClientResponse, bool) {
	if err == io.EOF {
		return nil, true
	}

	if err != nil {
		return checkSynthesisError(err, ctx, request), true
	}

	response := future.GetClientResponse()
	if response.Code != code.ServerOK {
		err = errors.New(fmt.Sprintf("grpc params error: %s", response.Status))
	} else {
		delayTime := float64(time.Now().Sub(lastStartTime).Milliseconds())
		response.DelayTime = delayTime
		if rtf := future.GetRtf(); rtf > 0 {
			metrics.DurationObserve(
				metrics.RealtimeRtfDuration, metrics.RealtimeRtfBucket, request.RobotName, request.TTSType, request.Locale, request.Speaker,
				request.Company, request.ServerName, request.Stream, future.GetRtf())
		}
		if request.Stream && delayTime > 0 && lastIndex > 1 {
			metrics.DurationObserve(
				metrics.RealtimeEveryFrameDuration, metrics.RealtimeEveryFrameBucket, request.RobotName, request.TTSType, request.Locale, request.Speaker,
				request.Company, request.ServerName, request.Stream, delayTime)
		} else if lastIndex == 1 && request.Stream {
			metrics.DurationObserve(
				metrics.RealtimeFirstFrameDuration, metrics.RealtimeFirstFrameBucket, request.RobotName, request.TTSType, request.Locale, request.Speaker,
				request.Company, request.ServerName, request.Stream, delayTime)
		}
		log.Infof(ctx, "接收到tts数据延迟：current_time=%v, index=%d, duration=%d, audioContent=%d, serverName=%s, %f", time.Now(), lastIndex, response.Duration, len(response.AudioContent), request.ServerName, delayTime)
	}

	return NewAudioClientResponse(response, response.Code, err), future.GetIsEnd()
}

func (grpcClient *GrpcClient) SynthesisMicrosoftAudio(ctx context.Context, ttsRequest *microsoftTTS.MicrosoftTtsRequest, request *models.TTSRequest) <-chan *AudioClientResponse {
	client := microsoft.GetStreamTTSClient(ctx)
	audioChannel := make(chan *AudioClientResponse, 0)

	go func() {
		defer func() {
			close(audioChannel)
		}()

		var isEnd = false
		log.Infof(ctx, "SynthesisAudio microsoft message: %v", ttsRequest)
		stream, err := client.Synthesis(ctx, ttsRequest)
		if err != nil {
			audioChannel <- checkSynthesisError(err, ctx, request)
			isEnd = true
		}

		var response *AudioClientResponse
		lastStartTime, lastIndex := time.Now(), 1
		for !isEnd {
			future, err := stream.Recv()
			response, isEnd = handleSynthesisResponse(
				ctx, request, &grpc_response.MicrosoftResponse{Future: future}, err, lastStartTime, lastIndex)
			if response != nil {
				audioChannel <- response
			}
			lastStartTime = time.Now()
			lastIndex += 1
		}
	}()
	return audioChannel
}

func (grpcClient *GrpcClient) SynthesisAudio(ctx context.Context, ttsRequest *pb.TtsRequest, request *models.TTSRequest) <-chan *AudioClientResponse {
	var client pb.TtsClient
	client, ctx = getDefaultClient(ctx, request.ServerName)
	audioChannel := make(chan *AudioClientResponse, 0)

	go func() {
		defer func() {
			close(audioChannel)
		}()

		var isEnd = false
		log.Infof(ctx, "SynthesisAudio message: %v", ttsRequest)
		stream, err := client.Synthesis(ctx, ttsRequest)
		if err != nil {
			audioChannel <- checkSynthesisError(err, ctx, request)
			isEnd = true
		}

		var response *AudioClientResponse
		lastStartTime, lastIndex := time.Now(), 1
		for !isEnd {
			future, err := stream.Recv()
			response, isEnd = handleSynthesisResponse(
				ctx, request, &grpc_response.AirudderResponse{Future: future}, err, lastStartTime, lastIndex)
			if response != nil {
				audioChannel <- response
			}
			lastStartTime = time.Now()
			lastIndex += 1
		}
	}()

	return audioChannel
}

func (grpcClient *GrpcClient) ConcatSynthesisAudio(ctx context.Context, ttsRequest *pb.TtsRequestForConcat, request *models.TTSRequest) <-chan *AudioClientResponse {
	var client pb.TtsClient
	client, ctx = getDefaultClient(ctx, request.ServerName)
	audioChannel := make(chan *AudioClientResponse, 0)

	go func() {
		defer func() {
			close(audioChannel)
		}()

		var isEnd = false
		stream, err := client.ConcatSynthesis(ctx, ttsRequest)
		if err != nil {
			audioChannel <- checkSynthesisError(err, ctx, request)
			isEnd = true
		}

		var response *AudioClientResponse
		lastStartTime, lastIndex := time.Now(), 1
		for !isEnd {
			future, err := stream.Recv()
			response, isEnd = handleSynthesisResponse(
				ctx, request, &grpc_response.AirudderResponse{Future: future}, err, lastStartTime, lastIndex)
			if response != nil {
				audioChannel <- response
			}
			lastStartTime = time.Now()
			lastIndex += 1
		}
	}()

	return audioChannel
}

func (grpcClient *GrpcClient) GetAudio(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, _ int) (*params.ClientResponse, int, error) {
	for response := range grpcClient.GetStreamAudio(ctx, request, ttsParams, false) {
		return response.Response, response.ErrorCode, response.Err
	}
	return nil, code.ErrorRequestInternal, errors.New("request ai tts failed")
}

func (grpcClient *GrpcClient) GetStreamAudio(ctx context.Context, request *models.TTSRequest, params *params.TtsParams, stream bool) <-chan *AudioClientResponse {
	if _, provider := request.GetProvider(); provider == constants.MicrosoftProvider || provider == constants.ElevenLabsProvider {
		microsoftRequest := &microsoftTTS.MicrosoftTtsRequest{
			RequestId:    request.CallId,
			Locale:       params.ConvertLocale(request.Locale, provider, request.IsElevenlabsV2()),
			Speaker:      params.ConvertSpeaker(request.Speaker, provider),
			SpeakingRate: request.SpeakingRate,
			Volume:       request.VolumeGainDb,
			Ssml:         params.GetTtsSSML(request, provider),
			Provider:     provider,
		}

		if provider == constants.ElevenLabsProvider {
			microsoftRequest.StreamingLatency = config.StreamingLatency
			microsoftRequest.Stability = config.Stability
			microsoftRequest.SimilarityBoost = config.SimilarityBoost
			microsoftRequest.ElevenlabsKey = params.GetElevenlabsKey(request.Speaker)
		}

		return grpcClient.SynthesisMicrosoftAudio(ctx, microsoftRequest, request)
	}

	ttsRequest := &pb.TtsRequest{
		Config: grpcClient.newTTSConfig(request, params, stream),
		Text:   request.Text,
	}

	return grpcClient.SynthesisAudio(ctx, ttsRequest, request)
}

func (grpcClient *GrpcClient) GetStreamAudioByGrpcRequest(ctx context.Context, request *realtimeTTS.Config, ttsRequest *models.TTSRequest, params *params.TtsParams) <-chan *AudioClientResponse {
	if len(request.GetReferenceAudioContents()) == 0 && len(request.GetText()) == 1 {
		return grpcClient.GetStreamAudio(ctx, ttsRequest, params, request.Stream)
	}

	ttsRequestForConcat := &pb.TtsRequestForConcat{
		Config:                 grpcClient.newTTSConfig(ttsRequest, params, request.Stream),
		Text:                   request.GetText(),
		RefText:                request.GetRefText(),
		ReferenceAudioContents: request.GetReferenceAudioContents(),
		TextAndAudioPositions:  request.GetTextAndAudioPositions(),
	}

	return grpcClient.ConcatSynthesisAudio(ctx, ttsRequestForConcat, ttsRequest)
}
