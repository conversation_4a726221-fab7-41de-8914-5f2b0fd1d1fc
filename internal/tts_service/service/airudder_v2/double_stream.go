package airudder_v2

import (
	"context"
	"github.com/pkg/errors"
	"google.golang.org/grpc/metadata"
	"io"
	"strings"
	"time"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/airudder_v2/grpc_response"
	"tts-service/internal/tts_service/service/convert"
	"tts-service/internal/tts_service/service/params"
	"tts-service/rpc/double_stream_tts"
	"tts-service/rpc/double_stream_tts/double_stream"
)

func getDefaultDoubleStreamClient(ctx context.Context, serverName string) (double_stream.TtsClient, context.Context) {
	client := double_stream_tts.GetDoubleStreamTTSClient(ctx)
	header := metadata.New(map[string]string{
		"tts-service-name": serverName,
	})

	ctx = metadata.NewOutgoingContext(ctx, header)

	return client, ctx
}

func DoubleStreamAdapter(text<PERSON><PERSON> chan string) convert.Adapter {
	return func(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, inPipe io.WriteCloser) error {
		var client double_stream.TtsClient
		client, ctx = getDefaultDoubleStreamClient(ctx, request.ServerName)

		stream, err := client.Synthesis(ctx)
		if err == nil {
			err = stream.Send(&double_stream.TtsRequest{
				Request: &double_stream.TtsRequest_Config{
					Config: &double_stream.TtsConfig{
						Voice: &double_stream.VoiceConfig{
							LanguageCode: strings.Split(request.Locale, "-")[0],
							VoiceId:      strings.Split(request.Speaker, "-")[2],
						},
						Audio: &double_stream.AudioConfig{
							AudioEncoding:   ttsParams.AudioConfig.AudioEncoding,
							SpeakingRate:    float32(ttsParams.AudioConfig.SpeakingRate),
							VolumeGainDb:    float32(ttsParams.AudioConfig.VolumeGainDb),
							SampleRateHertz: int32(ttsParams.AudioConfig.SampleRateHertz),
						},
					},
				},
			})
		}

		if err != nil {
			return errors.Wrap(err, "generate double stream audio error")
		}

		defer func(stream double_stream.Tts_SynthesisClient) {
			if err = stream.CloseSend(); err != nil {
				log.Infof(ctx, "[airudder_v2] double stream close send error: %v", err)
			}
		}(stream)

		go func() {
			var sendError error
			defer func() {
				if sendError != nil {
					err = errors.Wrap(sendError, "generate double stream audio error")
				}
			}()

			for text := range textChan {
				sendError = stream.Send(&double_stream.TtsRequest{
					Request: &double_stream.TtsRequest_Content{
						Content: &double_stream.TtsContent{
							Text: text,
						},
					},
				})

				if sendError != nil {
					return
				}
			}

			sendError = stream.Send(&double_stream.TtsRequest{
				Request: &double_stream.TtsRequest_Content{
					Content: &double_stream.TtsContent{
						IsEnd: true,
					},
				},
			})
		}()

		var response *AudioClientResponse
		lastStartTime, lastIndex := time.Now(), 1
		var isEnd = false
		for !isEnd && err == nil {
			future, receiveError := stream.Recv()
			if receiveError != nil {
				err = errors.Wrap(receiveError, "generate double stream audio error")
				continue
			}
			response, isEnd = handleSynthesisResponse(
				ctx, request, &grpc_response.DoubleStreamAirudderResponse{Future: future}, receiveError, lastStartTime, lastIndex)

			if response != nil && response.Response != nil {
				log.Infof(ctx, "[airudder_v2] double stream response: %d", len(response.Response.AudioContent))
				_, err = inPipe.Write(response.Response.AudioContent)
			}
			lastStartTime = time.Now()
			lastIndex += 1
		}

		return err
	}
}

func GenerateStreamAudio(ctx context.Context, request *models.TTSRequest, ttsParams *params.TtsParams, textChan chan string) <-chan *AudioClientResponse {
	var client double_stream.TtsClient
	client, ctx = getDefaultDoubleStreamClient(ctx, request.ServerName)
	audioChannel := make(chan *AudioClientResponse, 10)

	stream, err := client.Synthesis(ctx)
	if err == nil {
		err = stream.Send(&double_stream.TtsRequest{
			Request: &double_stream.TtsRequest_Config{
				Config: &double_stream.TtsConfig{
					Voice: &double_stream.VoiceConfig{
						LanguageCode: strings.Split(request.Locale, "-")[0],
						VoiceId:      strings.Split(request.Speaker, "-")[2],
					},
					Audio: &double_stream.AudioConfig{
						AudioEncoding:   ttsParams.AudioConfig.AudioEncoding,
						SpeakingRate:    float32(ttsParams.AudioConfig.SpeakingRate),
						VolumeGainDb:    float32(ttsParams.AudioConfig.VolumeGainDb),
						SampleRateHertz: int32(ttsParams.AudioConfig.SampleRateHertz),
					},
				},
			},
		})
	}

	if err != nil {
		audioChannel <- checkSynthesisError(errors.Wrap(err, "generate double stream audio error"), ctx, request)
		close(audioChannel)
		return audioChannel
	}

	go func() {
		var sendError error
		defer func() {
			if sendError != nil {
				log.Infof(ctx, "generate double stream audio error: %s", sendError.Error())
			}
		}()

		for text := range textChan {
			sendError = stream.Send(&double_stream.TtsRequest{
				Request: &double_stream.TtsRequest_Content{
					Content: &double_stream.TtsContent{
						Text: text,
					},
				},
			})

			if sendError != nil {
				return
			}
		}

		sendError = stream.Send(&double_stream.TtsRequest{
			Request: &double_stream.TtsRequest_Content{
				Content: &double_stream.TtsContent{
					IsEnd: true,
				},
			},
		})
	}()

	go func() {
		defer func() {
			close(audioChannel)
			_ = stream.CloseSend()
		}()

		var response *AudioClientResponse
		lastStartTime, lastIndex := time.Now(), 1
		var isEnd = false
		for !isEnd {
			future, receiveError := stream.Recv()
			if receiveError != nil {
				receiveError = errors.Wrap(receiveError, "generate double stream audio error")
			}
			response, isEnd = handleSynthesisResponse(
				ctx, request, &grpc_response.DoubleStreamAirudderResponse{Future: future}, receiveError, lastStartTime, lastIndex)

			if response != nil {
				audioChannel <- response
			}
			lastStartTime = time.Now()
			lastIndex += 1
		}
	}()

	return audioChannel
}
