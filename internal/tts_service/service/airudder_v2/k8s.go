package airudder_v2

import (
	"context"
	"github.com/go-resty/resty/v2"
	"strings"
	"sync"
	"time"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/log"
)

const (
	k8sClientTimeOut = 10 * time.Second
)

var (
	k8sClient *K8sClient
	k8sOnce   sync.Once
)

type K8sClient struct {
	http *resty.Client
}

func GetK8sClient() *K8sClient {
	k8sOnce.Do(func() {
		k8sClient = &K8sClient{
			http: resty.New().SetTimeout(k8sClientTimeOut),
		}
	})

	return k8sClient
}

func (client *K8sClient) pull(ctx context.Context, serverName string, appName string) {
	if strings.Contains(serverName, "realtime") {
		return
	}

	respBody := map[string]interface{}{
		"namespace":      config.Config.GetString("k8s.namespace"),
		"server_name":    serverName,
		"replica_number": config.Config.GetInt("k8s.replica"),
		"region":         config.Config.GetString("k8s.region"),
	}

	if appName != "" {
		respBody["app_name"] = appName
	}

	log.Infof(ctx, "pull k8s server: %v", respBody)
	_, err := client.http.R().
		SetHeader("Content-Type", "application/json; charset=UTF-8").
		SetBody(respBody).
		Post(config.Config.GetString("k8s.url"))

	if err != nil {
		log.Errorf(ctx, "pull k8s error：%s", err.Error())
	}
}
