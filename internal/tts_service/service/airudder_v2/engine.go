package airudder_v2

import (
	"context"
	"github.com/pkg/errors"
	"tts-service/internal/pkg/code"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service/params"
	"tts-service/pkg/pck2wav"
)

// EngineClient 自研获取音频接口
type EngineClient interface {
	GetAudio(ctx context.Context, request *models.TTSRequest, params *params.TtsParams, retryTimes int) (response *params.ClientResponse, errorCode int, err error)
}

type Engine struct {
	code    int
	err     error
	content []byte
}

func (engine *Engine) GetCode() int {
	return engine.code
}

func (engine *Engine) GetStatus() string {
	return engine.err.Error()
}

func (engine *Engine) GetAudio() *models.Wav {
	return &models.Wav{Content: engine.content}
}

func (engine *Engine) GenerateAudio(ctx context.Context, request *models.TTSRequest) bool {
	if request.ServerName == "" {
		engine.code = code.ErrorMissingParams
		engine.err = errors.New("k8s need server_name is miss or empty! return none")
		return false
	}

	ttsParams, err := params.NewTTSParams(request)
	if err != nil {
		engine.code = code.ErrorMissingParams
		engine.err = err
		return false
	}

	var client EngineClient
	if request.UseGrpc() {
		client = GetGrpcClient()
	} else {
		client = GetIngressClient()
	}

	result, errorCode, err := client.GetAudio(ctx, request, ttsParams, 2)
	if err != nil {
		engine.code = errorCode
		engine.err = err
		return false
	}

	if request.UseGrpc() {
		// 将音频从 pcm 格式转换为 wav
		result.AudioContent, err = pck2wav.Pcm2Wav(result.AudioContent, request.GetDefaultSampleRate())
		if err != nil {
			engine.code = code.ErrorInternal
			engine.err = errors.Wrap(err, "convert pcm to wav error")
			return false
		}
	}
	engine.content = result.AudioContent
	return true
}
