package params

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"strings"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/constants"
	"tts-service/internal/pkg/utils"
	"tts-service/internal/tts_service/models"
)

const (
	DefaultMinimaxModel = "speech-01-turbo"
	DefaultSSMLGender   = "FEMALE"
	AirudderPrefix      = "airudder_"

	DefaultAudioEncoding = "LINEAR16"

	ttsApiXMLPayload = `<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="%s"><voice name="%s">%s</voice></speak>`
)

type VoiceParams struct {
	LanguageCode string    `json:"languageCode"` //实际为locale?
	Name         string    `json:"name"`
	VoiceID      string    `json:"voiceID"`
	SSMLGender   string    `json:"ssmlGender"`
	TTSExtra     *TTSExtra `json:"tts_extra,omitempty"`
}

type TTSExtra struct {
	Stability              *float64 `json:"stability,omitempty" yaml:"stability" mapstructure:"stability"`
	ApplyTextNormalization *string  `json:"apply_text_normalization,omitempty" yaml:"apply_text_normalization" mapstructure:"apply_text_normalization"`
	LanguageCode           *bool    `json:"language_code,omitempty" yaml:"language_code" mapstructure:"language_code"`
	Pitch                  *int     `json:"pitch,omitempty" yaml:"pitch" mapstructure:"pitch"`
	Emotion                *string  `json:"emotion,omitempty" yaml:"emotion" mapstructure:"emotion"`
	LanguageBoost          *string  `json:"language_boost,omitempty" yaml:"language_boost" mapstructure:"language_boost"`
}

type AudioConfig struct {
	AudioEncoding   string  `json:"audioEncoding,omitempty"`
	VolumeGainDb    float64 `json:"volumeGainDb,omitempty"`
	SpeakingRate    float64 `json:"speakingRate,omitempty"`
	SampleRateHertz float64 `json:"sampleRateHertz,omitempty"`
}

type InputParams struct {
	SSML string `json:"ssml,omitempty"`
	Text string `json:"text,omitempty"`
}

type LLMParameter struct {
	Model string `json:"model"` //目前来源于version表单
}

type TtsParams struct {
	TTSType      string        `json:"tts_type"`
	Input        *InputParams  `json:"input"`
	Voice        *VoiceParams  `json:"voice"`
	AudioConfig  *AudioConfig  `json:"audioConfig"`
	Container    string        `json:"container"`
	LLMParameter *LLMParameter `json:"llmParameter"` //大模型tts相关配置
}

func (ttsParams *TtsParams) Provider() string {
	if ttsParams.TTSType == "" {
		if strings.HasPrefix(strings.ToLower(ttsParams.Voice.Name), constants.AirudderProviderPrefix) {
			return constants.AirudderV1Provider
		}

		return constants.GoogleProvider
	}

	providers := strings.Split(ttsParams.TTSType, constants.ProviderSplit)
	if len(providers) == 1 {
		return providers[0]
	}

	if providers[0] == constants.AirudderProviderPrefix {
		if providers[1] == constants.AirudderV1ProviderSuffix {
			return constants.AirudderV1Provider
		}

		if providers[1] == constants.AirudderV2ProviderSuffix {
			return constants.AirudderV2Provider
		}

	}

	switch providers[0] {
	case constants.GoogleProvider:
		return constants.GoogleProvider
	case constants.MicrosoftProvider:
		return constants.MicrosoftProvider
	case constants.ElevenLabsProvider:
		return constants.ElevenLabsProvider
	case constants.OpenAiProvider:
		return constants.OpenAiProvider
	case constants.XunfeiProvider:
		return constants.XunfeiProvider
	case constants.MinimaxProvider:
		return constants.MinimaxProvider
	case constants.DeepgramProvider:
		return constants.DeepgramProvider
	case constants.AliyunProvider:
		return constants.AliyunProvider
	default:
		return ""
	}
}

func (ttsParams *TtsParams) GetISO639Language() string {
	if ttsParams == nil || ttsParams.Voice == nil {
		return ""
	}

	return strings.Split(ttsParams.Voice.LanguageCode, "-")[0]
}

func (ttsParams *TtsParams) IsElevenlabsV2() bool {
	if ttsParams.TTSType == "" {
		return false
	}
	providers := strings.Split(ttsParams.TTSType, constants.ProviderSplit)
	if len(providers) < 2 {
		return false
	}
	return providers[0] == constants.ElevenLabsProvider && providers[1] == constants.AirudderV2ProviderSuffix
}

func (ttsParams *TtsParams) GetModel() string {

	if ttsParams != nil &&
		ttsParams.LLMParameter != nil &&
		ttsParams.LLMParameter.Model != "" {
		return ttsParams.LLMParameter.Model
	}

	//旧逻辑兜底
	switch ttsParams.Provider() {
	case constants.MinimaxProvider:
		return ttsParams.getLLMModel()
	case constants.ElevenLabsProvider:
		return ttsParams.ConvertLocale(
			ttsParams.Voice.LanguageCode,
			ttsParams.Provider(),
			ttsParams.IsElevenlabsV2(),
		)
	}
	return ""
}

func (ttsParams *TtsParams) GetStability() float64 {

	defaultV := float64(config.Stability) / 100

	if ttsParams != nil &&
		ttsParams.Voice != nil &&
		ttsParams.Voice.TTSExtra != nil {
		if ttsParams.Voice.TTSExtra.Stability != nil {
			return *ttsParams.Voice.TTSExtra.Stability
		}
	}

	//旧逻辑兜底
	return defaultV
}

func (ttsParams *TtsParams) ToMicrosoftSSML(request *models.TTSRequest) string {
	if ttsParams.Input.Text != "" {
		return fmt.Sprintf(ttsApiXMLPayload, request.Locale, request.Speaker, ttsParams.Input.Text)
	}

	return ttsParams.Input.SSML
}

func (ttsParams *TtsParams) ConvertLocale(locale, provider string, isElevenLabsV2 bool) string {
	if provider == constants.ElevenLabsProvider {
		matchLocale := locale
		if locales := strings.Split(locale, "-"); len(locales) > 1 {
			matchLocale = locales[0]
		}

		voiceMaps := config.LocaleVoiceMaps
		if isElevenLabsV2 {
			voiceMaps = config.V2LocaleVoiceMaps
		}

		if modelId, ok := voiceMaps[strings.ToLower(matchLocale)]; ok && modelId != "" {
			return modelId
		}

		if modelId, ok := voiceMaps["default"]; ok && modelId != "" {
			return modelId
		}

	}

	return locale
}

func (ttsParams *TtsParams) ConvertSpeaker(speaker, provider string) string {

	matchSpeaker := speaker
	if speakers := strings.Split(speaker, "-"); len(speakers) > 1 {
		matchSpeaker = speakers[len(speakers)-1]
	}
	switch {
	case provider == constants.DeepgramProvider:
		if voiceId, ok := config.DeepgramVoiceMaps[strings.ToLower(matchSpeaker)]; ok && voiceId != "" {
			return voiceId
		}

	case provider == constants.ElevenLabsProvider:
		if voiceId, ok := config.SpeakerVoiceMaps[strings.ToLower(matchSpeaker)]; ok && voiceId != "" {
			return voiceId
		}
	case provider == constants.XunfeiProvider ||
		provider == constants.OpenAiProvider ||
		provider == constants.AliyunProvider:
	case provider == constants.MinimaxProvider:
		if voiceId, ok := config.MinimaxVoiceMaps[matchSpeaker]; ok && voiceId != "" {
			return voiceId
		}
	}

	//配置了voiceID，直接返回
	if ttsParams.Voice != nil && ttsParams.Voice.VoiceID != "" {
		return ttsParams.Voice.VoiceID
	}

	return matchSpeaker
}

func (ttsParams *TtsParams) GetElevenlabsKey(speaker string) string {
	matchSpeaker := speaker
	if speakers := strings.Split(speaker, "-"); len(speakers) > 1 {
		matchSpeaker = speakers[len(speakers)-1]
	}

	if key, ok := config.SpeakerKeyMaps[strings.ToLower(matchSpeaker)]; ok && key != "" {
		return key
	}

	return config.ElevenlabsDefaultKey
}

func (ttsParams *TtsParams) GetTtsSSML(request *models.TTSRequest, provider string) string {
	switch {
	case provider == constants.MicrosoftProvider:
		return ttsParams.ToMicrosoftSSML(request)
	case provider == constants.ElevenLabsProvider || provider == constants.OpenAiProvider || provider == constants.DeepgramProvider || provider == constants.AliyunProvider:
		if ttsParams.Input.SSML != "" {
			return utils.ClearSSMLText(ttsParams.Input.SSML)
		}
	case provider == constants.XunfeiProvider:
		switch {
		case ttsParams.Input.Text != "":
			return base64.StdEncoding.EncodeToString([]byte(ttsParams.Input.Text))
		case ttsParams.Input.SSML != "":
			return base64.StdEncoding.EncodeToString([]byte(utils.ClearSSMLText(ttsParams.Input.SSML)))
		}
	case provider == constants.MinimaxProvider:
		switch {
		case ttsParams.Input.Text != "":
			return ttsParams.Input.Text
		case ttsParams.Input.SSML != "":
			return utils.ClearSSMLText(ttsParams.Input.SSML)
		}
	}

	return ttsParams.Input.Text
}

func (ttsParams *TtsParams) getLLMModel() string {
	if ttsParams.LLMParameter == nil {
		return DefaultMinimaxModel
	}
	if ttsParams.LLMParameter.Model != "" {
		return ttsParams.LLMParameter.Model
	}
	return DefaultMinimaxModel
}

func (ttsParams *TtsParams) GetSpeed() float64 {
	if ttsParams == nil || ttsParams.AudioConfig == nil {
		return 1.0
	}
	switch ttsParams.Provider() {
	case constants.MinimaxProvider:
		if ttsParams.AudioConfig.SpeakingRate <= 2 &&
			ttsParams.AudioConfig.SpeakingRate >= 0.5 {
			return ttsParams.AudioConfig.SpeakingRate
		}
	case constants.ElevenLabsProvider:
		if ttsParams.AudioConfig.SpeakingRate <= 1.2 &&
			ttsParams.AudioConfig.SpeakingRate >= 0.7 {
			return ttsParams.AudioConfig.SpeakingRate
		}
	}
	return 1.0
}

func (ttsParams *TtsParams) GetVolume(provider string) float64 {
	if ttsParams == nil || ttsParams.AudioConfig == nil {
		return 1.0
	}
	switch provider {
	case constants.MinimaxProvider:
		if ttsParams.AudioConfig.VolumeGainDb > 0 &&
			ttsParams.AudioConfig.VolumeGainDb <= 10 {
			return ttsParams.AudioConfig.VolumeGainDb
		}
	}
	return 1.0
}

func (ttsParams *TtsParams) GetApplyTextNormalization() *string {

	if ttsParams != nil &&
		ttsParams.Voice != nil &&
		ttsParams.Voice.TTSExtra != nil {
		return ttsParams.Voice.TTSExtra.ApplyTextNormalization
	}

	return nil
}

func (ttsParams *TtsParams) GetLanguageCode() *bool {
	if ttsParams != nil &&
		ttsParams.Voice != nil &&
		ttsParams.Voice.TTSExtra != nil {
		return ttsParams.Voice.TTSExtra.LanguageCode
	}
	return nil
}

func (ttsParams *TtsParams) GetLanguageBoost() *string {
	if ttsParams != nil &&
		ttsParams.Voice != nil &&
		ttsParams.Voice.TTSExtra != nil {

		if ttsParams.Voice.TTSExtra.LanguageBoost == nil {
			return nil
		}

		switch {
		case *ttsParams.Voice.TTSExtra.LanguageBoost == "on":
			return ttsParams.GetStandardLanguage()
		case *ttsParams.Voice.TTSExtra.LanguageBoost == "off":
			return nil
		case *ttsParams.Voice.TTSExtra.LanguageBoost == "auto":
			return ttsParams.Voice.TTSExtra.LanguageBoost
		default:
			return nil
		}
	}
	return nil
}

func (ttsParams *TtsParams) GetPitch() int {
	if ttsParams != nil &&
		ttsParams.Voice != nil &&
		ttsParams.Voice.TTSExtra != nil &&
		ttsParams.Voice.TTSExtra.Pitch != nil {
		return *ttsParams.Voice.TTSExtra.Pitch
	}
	return 0

}

func (ttsParams *TtsParams) GetEmotion() *string {
	if ttsParams != nil &&
		ttsParams.Voice != nil &&
		ttsParams.Voice.TTSExtra != nil {
		return ttsParams.Voice.TTSExtra.Emotion
	}
	return nil
}

func (ttsParams *TtsParams) GetStandardLanguage() *string {

	language := utils.GetStandardLanguage(ttsParams.GetISO639Language())
	switch ttsParams.Provider() {
	case constants.MinimaxProvider:
		//粤语特殊逻辑
		if ttsParams.Voice.LanguageCode == "yue-CN" {
			language = "Chinese,Yue"
		}
	}
	if language != "" {
		return &language
	}
	return nil
}

func newInputParams(request *models.TTSRequest) (*InputParams, error) {
	if utils.IsSSMLText(request.Text) {
		return &InputParams{SSML: request.Text}, nil
	} else {
		return &InputParams{Text: request.Text}, nil
	}
}

func newVoiceParams(request *models.TTSRequest) (*VoiceParams, error) {
	voice := &VoiceParams{
		LanguageCode: request.Locale,
		Name:         request.Speaker,
		VoiceID:      request.VoiceID,
		SSMLGender:   DefaultSSMLGender,
	}

	if strings.HasPrefix(voice.Name, AirudderPrefix) {
		voice.Name = voice.Name[len(AirudderPrefix):]
	}

	if voice.LanguageCode == "" || voice.Name == "" {
		return nil, errors.New("airudder v2, language_code or speaker error")
	}

	if request.Extra != "" {
		if err := json.Unmarshal([]byte(request.Extra), &voice.TTSExtra); err != nil {
			return nil, errors.Wrap(err, "unmarshal extra params error")
		}
	}
	return voice, nil
}

func newAudioConfig(request *models.TTSRequest) *AudioConfig {
	audioConfig := AudioConfig{
		AudioEncoding:   request.AudioEncoding,
		VolumeGainDb:    utils.CheckVolumeGainDb(request.VolumeGainDb),
		SpeakingRate:    request.SpeakingRate,
		SampleRateHertz: request.SampleRate,
	}

	if audioConfig.SpeakingRate == 0 {
		audioConfig.SpeakingRate = 1
	}

	if audioConfig.AudioEncoding == "" {
		audioConfig.AudioEncoding = DefaultAudioEncoding
	}

	return &audioConfig
}

func NewTTSParams(request *models.TTSRequest) (*TtsParams, error) {
	voice, err := newVoiceParams(request)
	if err != nil {
		return nil, err
	}

	input, err := newInputParams(request)
	if err != nil {
		return nil, err
	}

	params := TtsParams{
		TTSType:      request.TTSType,
		Input:        input,
		Voice:        voice,
		AudioConfig:  newAudioConfig(request),
		LLMParameter: &LLMParameter{Model: request.Version},
	}

	return &params, nil
}

type WordResponse struct {
	Word     string `json:"word"`
	Duration int64  `json:"duration"`
}

type ClientResponse struct {
	AudioContent []byte         `json:"audioContent"`
	Code         int            `json:"code"`
	Status       string         `json:"status"`
	Duration     int64          `json:"duration"`
	IsEnd        bool           `json:"isEnd"`
	Words        []WordResponse `json:"words"`
	DelayTime    float64        `json:"delayTime"`
}
