package tts_service

import (
	"context"
	"fmt"
	"github.com/judwhite/go-svc"
	"math/rand"
	"os"
	"sync"
	"time"
	"tts-service/internal/pkg/alarm"
	"tts-service/internal/pkg/cache"
	"tts-service/internal/pkg/code"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/environment"
	"tts-service/internal/pkg/gcp"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/grpc_server"
	"tts-service/internal/tts_service/grpc_server/realtime_tts"
	"tts-service/internal/tts_service/service/google"
	"tts-service/internal/tts_service/service/microsoft"
)

type TTSService struct {
	once       sync.Once
	Context    context.Context
	httpServer *httpServer
	grpcServer *grpc_server.GrpcServer
}

func (service *TTSService) Init(_ svc.Environment) (err error) {
	if gcpServerCheck := os.Getenv("gcp.check_address"); gcpServerCheck != "" {
		fmt.Println("开始检测 gcp")
		for !gcp.CheckHealthy(gcpServerCheck) {
			fmt.Println("gcp 检查失败，等待重试")
			time.Sleep(100 * time.Millisecond)
		}
		fmt.Println("gcp检测成功")
	}

	// 初始化随机数
	rand.Seed(time.Now().UTC().UnixNano())
	service.Context = context.Background()

	// 初始化环境变量
	environment.Env.Set(config.GetEnvByFlag())

	// 初始化配置数据
	if environment.Env.IsLocal() || environment.Env.IsDev() {
		err = config.InitConfig(fmt.Sprintf("config/%s", environment.Env))
		config.UpdateCacheConfig()
		config.UpdateElevenlabsConfig()
		config.UpdateOpenAiConfig()
		config.UpdateAliyunConfig()
		config.UpdateXunfeiConfig()
		config.UpdateDeepgramConfig()
		config.UpdateMiniMaxConfig()
	} else {
		err = config.InitConfigFromApollo(
			config.UpdateCacheConfig,
			config.UpdateElevenlabsConfig,
			config.UpdateOpenAiConfig,
			config.UpdateAliyunConfig,
			config.UpdateXunfeiConfig,
			config.UpdateDeepgramConfig,
			config.UpdateMiniMaxConfig,
		)
	}

	// 初始化日志
	log.InitLogger()

	// 初始化 Google 账号信息
	if err == nil {
		err = google.InitClients(service.Context)
	}

	// 初始化 microsoft 账号信息
	if err == nil {
		err = microsoft.InitClients(service.Context)
	}

	if err != nil {
		return err
	}
	// 初始化httpServer
	service.httpServer = newHttpServer(service.Context, config.Config.GetInt("http_port"))
	service.grpcServer, err = grpc_server.NewGrpcServer(service.Context, config.Config.GetInt("grpc_port"))

	// 初始化 redis 缓存
	cache.InitFileCacheClient()
	config.ReloadCacheMap()
	log.Info(service.Context, "Init Success")
	return err
}

func (service *TTSService) Start() error {
	// http、grpc 服务启动
	service.httpServer.start()
	service.grpcServer.Start()
	log.Info(service.Context, "TTS Service Start")
	return nil
}

func (service *TTSService) Stop() error {
	// http、grpc 服务关闭

	if realtime_tts.StreamCount.Load() != 0 {
		ticker := time.NewTicker(10 * time.Second)
		timeoutTicker := time.NewTimer(10 * 60 * time.Second)
		defer ticker.Stop()
		defer timeoutTicker.Stop()

	waitingTtsOver:
		for {
			select {
			case <-ticker.C:
				if realtime_tts.StreamCount.Load() == 0 {
					break waitingTtsOver
				}
			case <-timeoutTicker.C:
				break waitingTtsOver
			}
		}

	}

	alarm.P2WithContent(
		service.Context,
		"TTS-Service服务正常关闭成功",
		code.ServerOK,
		alarm.NewPairs("关闭原因", fmt.Sprintf("正常关闭")),
		alarm.NewPairs("剩余连接数", realtime_tts.StreamCount.String()),
	)

	service.httpServer.stop()
	service.grpcServer.Stop()

	log.Info(service.Context, "TTS Service End")
	_ = log.Log.Sync()
	return nil
}
