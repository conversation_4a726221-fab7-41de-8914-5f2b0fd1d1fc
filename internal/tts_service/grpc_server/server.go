package grpc_server

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"google.golang.org/grpc"
	"net"
	"tts-service/internal/pkg/log"
	"tts-service/internal/tts_service/grpc_server/realtime_tts"
	"tts-service/rpc/service/pb"
)

type GrpcServer struct {
	ctx        context.Context
	grpcServer *grpc.Server
	listener   net.Listener
}

func NewGrpcServer(ctx context.Context, port int) (*GrpcServer, error) {
	server := grpc.NewServer()
	// 注册 grpc 服务
	pb.RegisterRealtimeTTSServer(server, &realtime_tts.RealtimeTtsGrpcServer{})

	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		return nil, errors.Wrap(err, "grpc server init error")
	}

	return &GrpcServer{
		ctx:        ctx,
		grpcServer: server,
		listener:   lis,
	}, nil
}

func (server *GrpcServer) Start() {
	go func(s *GrpcServer) {
		if err := s.grpcServer.Serve(s.listener); err != nil {
			log.Errorf(s.ctx, "grpc error: %s", err.Error())
			return
		}
	}(server)
}

func (server *GrpcServer) Stop() {
	server.grpcServer.GracefulStop()
}
