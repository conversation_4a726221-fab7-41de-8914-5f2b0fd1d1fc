package realtime_tts

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"strings"
	"time"
	"tts-service/internal/pkg/alarm"
	"tts-service/internal/pkg/code"
	config2 "tts-service/internal/pkg/config"
	"tts-service/internal/pkg/constants"
	"tts-service/internal/pkg/log"
	"tts-service/internal/pkg/metrics"
	"tts-service/internal/pkg/utils"
	"tts-service/internal/pkg/wav"
	"tts-service/internal/tts_service/models"
	"tts-service/internal/tts_service/service"
	"tts-service/internal/tts_service/service/airudder_v2"
	"tts-service/internal/tts_service/service/aliyun"
	"tts-service/internal/tts_service/service/convert"
	"tts-service/internal/tts_service/service/deepgram"
	"tts-service/internal/tts_service/service/elevenlabs"
	"tts-service/internal/tts_service/service/minimax"
	"tts-service/internal/tts_service/service/openai"
	"tts-service/internal/tts_service/service/params"
	"tts-service/internal/tts_service/service/xunfei"
	"tts-service/rpc/service/pb"
)

type AudioResponseChan struct {
	Response *AudioResponse
	Err      error
}

func ConvertClientResult(ctx context.Context, request *models.TTSRequest, result *params.ClientResponse, errorCode int, err error) AudioResponseChan {
	response := &AudioResponse{
		GenerateTime: time.Now().UnixMilli(),
	}
	if err != nil {
		response.Code = errorCode
		response.IsEnd = true
		// 报警服务
		go alarm.P3(ctx, request, errorCode, err.Error(), !code.CheckCodeNeedRepeated(errorCode))
		return AudioResponseChan{
			Response: response,
			Err:      err,
		}
	}

	if result.Code == 0 {
		result.Code = code.ServerOK
	}

	response.Duration = float32(result.Duration)
	response.Code = result.Code
	response.DelayTime = result.DelayTime
	response.Content = result.AudioContent
	response.Status = result.Status
	response.IsEnd = result.IsEnd
	response.Words = result.Words
	response.GeneratedTime = time.Now().UnixMilli()

	return AudioResponseChan{
		Response: response,
		Err:      err,
	}
}

func GenerateDoubleStreamAudio(ctx context.Context, config *pb.Config) (chan string, chan AudioResponseChan) {
	responseChan := make(chan AudioResponseChan, 10)
	textChan := make(chan string, 10)

	go func() {
		defer func() {
			close(responseChan)
		}()

		response := &AudioResponse{}
		request := NewRequestFromConfig(config)
		ttsParams, err := params.NewTTSParams(request)
		if err != nil {
			response.Code = code.ErrorMissingParams
			responseChan <- AudioResponseChan{
				Response: response,
				Err:      err,
			}
			return
		}

		_, provider := request.GetProvider()
		switch provider {
		case constants.ElevenLabsProvider:
			for audio := range convert.PcmConvertAdapter(ctx, request, ttsParams, elevenlabs.DoubleStreamAdapter(textChan)) {
				responseChan <- ConvertClientResult(ctx, request, audio.Response, audio.ErrorCode, audio.Err)
			}
		case constants.AirudderV2Provider:
			for audio := range airudder_v2.GenerateStreamAudio(ctx, request, ttsParams, textChan) {
				responseChan <- ConvertClientResult(ctx, request, audio.Response, audio.ErrorCode, audio.Err)
			}
		default:
			response.Code = code.ErrorRequestInternal
			responseChan <- AudioResponseChan{
				Response: response,
				Err:      errors.New("provider not support"),
			}
		}
	}()

	return textChan, responseChan
}

func NewRequestFromConfig(config *pb.Config) *models.TTSRequest {
	text := ""
	if len(config.Text) > 0 {
		text = config.Text[0]
	}
	return &models.TTSRequest{
		TTSType:      config.TtsType,
		Speaker:      config.GetSpeaker(),
		Locale:       config.GetLocale(),
		Text:         text,
		Texts:        config.GetText(),
		AccountType:  config.GetAccountType(),
		ServerName:   config.GetServerName(),
		SampleRate:   float64(config.GetSampleRate()),
		VolumeGainDb: float64(config.GetVolume()),
		SpeakingRate: config2.GetRobotSpeakingRate(config.GetRobotName(), utils.Float32To64(config.GetSpeakingRate())),
		CallId:       config.GetCallId(),
		Mode:         config.GetMode(),
		AppName:      config.GetAppName(),
		RobotName:    config.GetRobotName(),
		Company:      config.GetCompany(),
		Stream:       config.Stream,
		Version:      config.GetVersion(),
		VoiceID:      config.GetVoiceId(),
		Extra:        config.GetExtra(),
		RequestKey:   config.GetRequestKey(),
		StateID:      config.GetStateId(),
	}
}

// GenerateAudio 生成音频数据
func GenerateAudio(ctx context.Context, config *pb.Config) chan AudioResponseChan {
	responseChan := make(chan AudioResponseChan, 0)
	go func() {
		defer func() {
			close(responseChan)
		}()

		response := &AudioResponse{}
		// 如果需要生成的 Text 为空
		if len(config.Text) == 0 {
			response.Code = code.ErrorMissingParams
			responseChan <- AudioResponseChan{
				Response: response,
				Err:      errors.New("text ttsParams is empty"),
			}
			return
		}

		// 构造和 http 请求一样的 request 参数
		request := NewRequestFromConfig(config)
		// 将 request 转换为 ttsParams 参数
		ttsParams, err := params.NewTTSParams(request)
		if err != nil {
			response.Code = code.ErrorMissingParams
			responseChan <- AudioResponseChan{
				Response: response,
				Err:      err,
			}
			return
		}

		_, provider := request.GetProvider()
		if provider == "" {
			provider = constants.AirudderV2Provider
		}

		switch {
		case provider == constants.XunfeiProvider ||
			provider == constants.MinimaxProvider:

			var adapter convert.Adapter
			switch provider {
			case constants.MinimaxProvider:
				adapter = minimax.StreamAdapter
			case constants.XunfeiProvider:
				adapter = xunfei.StreamAdapter
			}
			index := 1
			lastStartTime := time.Now()
			for audio := range convert.EmptyAdapter(ctx, request, ttsParams, adapter) {
				if audio.Err == nil && request.Stream && audio.Response != nil {
					delayTime := float64(time.Now().Sub(lastStartTime).Milliseconds())
					if index == 1 {
						metrics.DurationObserve(
							metrics.RealtimeFirstFrameDuration, metrics.RealtimeFirstFrameBucket, request.RobotName, request.TTSType, request.Locale, request.Speaker,
							request.Company, request.ServerName, request.Stream, delayTime)
					} else {
						metrics.DurationObserve(
							metrics.RealtimeEveryFrameDuration, metrics.RealtimeEveryFrameBucket, request.RobotName, request.TTSType, request.Locale, request.Speaker,
							request.Company, request.ServerName, request.Stream, delayTime)
					}
					lastStartTime = time.Now()
					log.Infof(ctx, "xunfei接收到tts数据延迟：current_time=%v, index=%d, duration=%f, audioContent=%d, serverName=%s, %f", time.Now(), index, response.Duration, audio.Response.Duration, request.ServerName, delayTime)
				}

				responseChan <- ConvertClientResult(ctx, request, audio.Response, audio.ErrorCode, audio.Err)
				index += 1
			}

		case provider == constants.ElevenLabsProvider ||
			provider == constants.OpenAiProvider ||
			provider == constants.DeepgramProvider ||
			provider == constants.AliyunProvider:
			index := 1
			lastStartTime := time.Now()
			var audioChan <-chan *convert.StreamAudio
			// Deepgram 使用单独的转换方法
			switch {
			case provider == constants.DeepgramProvider:
				audioChan = convert.ConvertAdapter(ctx, request, ttsParams, deepgram.GetStreamResponse)
			case provider == constants.AliyunProvider:
				audioChan = aliyun.StreamAdapter(ctx, request, ttsParams)
			case provider == constants.ElevenLabsProvider:
				if _, ok := config2.ElevenlabsMultiContextConfig[strings.ToLower(request.RobotName)]; ok {
					audioChan = elevenlabs.GetStreamResponseMultiContext(ctx, request, ttsParams)
					if audioChan == nil {
						break
					}
				}

				if _, ok := config2.ElevenlabsMultiContextConfig[request.AccountType]; ok {
					audioChan = elevenlabs.GetStreamResponseMultiContext(ctx, request, ttsParams)
					if audioChan == nil {
						break
					}
				}
				adapter := elevenlabs.StreamAdapter
				audioChan = convert.EmptyAdapter(ctx, request, ttsParams, adapter)
			case provider == constants.OpenAiProvider:
				adapter := openai.StreamAdapter
				audioChan = convert.PcmConvertAdapter(ctx, request, ttsParams, adapter)
			}

			for audio := range audioChan {
				if audio.Err == nil && request.Stream && audio.Response != nil {
					delayTime := float64(time.Now().Sub(lastStartTime).Milliseconds())
					if index == 1 {
						metrics.DurationObserve(
							metrics.RealtimeFirstFrameDuration, metrics.RealtimeFirstFrameBucket, request.RobotName, request.TTSType, request.Locale, request.Speaker,
							request.Company, request.ServerName, request.Stream, delayTime)
					} else {
						metrics.DurationObserve(
							metrics.RealtimeEveryFrameDuration, metrics.RealtimeEveryFrameBucket, request.RobotName, request.TTSType, request.Locale, request.Speaker,
							request.Company, request.ServerName, request.Stream, delayTime)
					}
					lastStartTime = time.Now()
					log.Infof(ctx, "接收到tts数据延迟：current_time=%v, index=%d, duration=%f, audioContent=%d, serverName=%s, %f", time.Now(), index, response.Duration, audio.Response.Duration, request.ServerName, delayTime)
				}

				responseChan <- ConvertClientResult(ctx, request, audio.Response, audio.ErrorCode, audio.Err)
				index += 1
			}
		case config.GetTtsV3() || provider == constants.MicrosoftProvider:
			log.Infof(ctx, "use grpc client: %s", config.TtsKey)
			client := airudder_v2.GetGrpcClient()
			for clientResponse := range client.GetStreamAudioByGrpcRequest(ctx, config, request, ttsParams) {
				// 特定的异常错误，需要发送给调度
				if config.Stream && provider == constants.AirudderV2Provider {
					switch clientResponse.ErrorCode {
					case code.ErrorContainer:
						go alarm.P1(ctx, request, code.RealtimeErrorContainer,
							fmt.Sprintf("TTS实例未启动 - %s", request.ServerName), false)
					case code.ErrorInternal:
						if strings.Contains(clientResponse.Err.Error(), "ResourceExhausted") {
							go alarm.P1(ctx, request, code.RealtimeErrorInternal,
								fmt.Sprintf("TTS实例启动过少 - %s: %s", request.ServerName, clientResponse.Err.Error()), false)
						}
					}
				}

				responseChan <- ConvertClientResult(ctx, request, clientResponse.Response, clientResponse.ErrorCode, clientResponse.Err)
			}
		default:
			log.Infof(ctx, "use http client: %s", config.TtsKey)
			startTime := time.Now()
			log.Infof(ctx, "选择provider=%s进行操作", provider)
			engine := service.GetEngineByProvider(provider)
			if engine.GenerateAudio(ctx, request) {
				result := &params.ClientResponse{
					AudioContent: engine.GetAudio().Content,
					Code:         code.ServerOK,
					IsEnd:        true,
					DelayTime:    float64(time.Now().Sub(startTime).Milliseconds()),
				}
				var wavAudio *wav.File
				wavAudio, err = wav.GetWavAudio(result.AudioContent)
				if err == nil {
					result.Duration = int64(wavAudio.Duration() / 1e3)
					result.AudioContent = wavAudio.Bytes()
				}
				responseChan <- ConvertClientResult(ctx, request, result, code.ServerOK, err)
			} else {
				responseChan <- ConvertClientResult(ctx, request, nil, engine.GetCode(), errors.New(engine.GetStatus()))
			}
		}
	}()

	return responseChan
}
