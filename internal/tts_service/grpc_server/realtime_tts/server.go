package realtime_tts

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/prometheus/client_golang/prometheus"
	"go.uber.org/atomic"
	"io"
	"strconv"
	"strings"
	"time"
	"tts-service/internal/pkg/cache"
	"tts-service/internal/pkg/code"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/log"
	"tts-service/internal/pkg/metrics"
	"tts-service/internal/pkg/utils"
	"tts-service/internal/tts_service/service/params"
	"tts-service/rpc/service/pb"
)

var (
	StreamCount = atomic.Int64{}
)

type RealtimeTtsGrpcServer struct {
	pb.UnimplementedRealtimeTTSServer
}

type AudioResponse struct {
	Content       []byte                `json:"content,omitempty"`
	Length        int                   `json:"length,omitempty"`
	Code          int                   `json:"code,omitempty"`
	Status        string                `json:"status,omitempty"`
	GenerateTime  int64                 `json:"generate_time,omitempty"`
	GeneratedTime int64                 `json:"generated_time,omitempty"`
	Duration      float32               `json:"duration,omitempty"`
	IsEnd         bool                  `json:"isEnd,omitempty"`
	Words         []params.WordResponse `json:"words"`
	DelayTime     float64               `json:"delayTime"`
}

func (server *RealtimeTtsGrpcServer) AllowCacheV2(pbConfig *pb.Config) bool {
	_, ok := config.EnableCacheV2Robots[pbConfig.GetRobotName()]
	return ok
}

func (server *RealtimeTtsGrpcServer) AllowCache(pbConfig *pb.Config) bool {
	if _, ok := config.DisableRobotCaches[pbConfig.GetRobotName()]; ok {
		return false
	}

	if _, ok := config.DisableServerNameCaches[pbConfig.GetServerName()]; ok {
		return false
	}

	return true
}

func (server *RealtimeTtsGrpcServer) ConvertPbConfigForData(pbConfig *pb.Config) map[string]interface{} {
	return map[string]interface{}{
		"Timestamp":              pbConfig.Timestamp,
		"Speaker":                pbConfig.Speaker,
		"TtsKey":                 pbConfig.TtsKey,
		"UniqueKey":              pbConfig.UniqueKey,
		"TtsType":                pbConfig.TtsType,
		"SpeakingRate":           pbConfig.SpeakingRate,
		"Volume":                 pbConfig.Volume,
		"Mode":                   pbConfig.Mode,
		"ServerName":             pbConfig.ServerName,
		"SampleRate":             pbConfig.SampleRate,
		"FromWhere":              pbConfig.FromWhere,
		"AccountType":            pbConfig.AccountType,
		"Locale":                 pbConfig.Locale,
		"CallId":                 pbConfig.CallId,
		"TtsV3":                  pbConfig.TtsV3,
		"Text":                   pbConfig.Text,
		"RefText":                pbConfig.RefText,
		"ReferenceAudioContents": len(pbConfig.ReferenceAudioContents),
		"TextAndAudioPositions":  pbConfig.TextAndAudioPositions,
		"RobotName":              pbConfig.RobotName,
		"Company":                pbConfig.Company,
		"AppName":                pbConfig.AppName,
		"Stream":                 pbConfig.Stream,
		"version":                pbConfig.Version,
		"VoiceID":                pbConfig.GetVoiceId(),
		"Extra":                  pbConfig.GetExtra(),
	}
}

// 返回用来做缓存的唯一Key
func (server *RealtimeTtsGrpcServer) getUniqueKey(pbConfig *pb.Config) string {
	ttsKey := pbConfig.GetUniqueKey()
	if ttsKey == "" {
		ttsKey = pbConfig.GetTtsKey()
	}
	// ttsKey + Text 支持Replace
	key := fmt.Sprintf(
		"realtime_tts_%s_%s_%s_%s_%s_%f_%f_%s_%v_v6",
		config.GetReplaceText(pbConfig.GetLocale(), ttsKey),
		pbConfig.GetText(),
		pbConfig.GetTtsType(),
		config.GetReplaceSpeaker(pbConfig.GetServerName(), pbConfig.GetSpeaker()),
		pbConfig.GetServerName(),
		config.GetRobotSpeakingRate(pbConfig.GetRobotName(), float64(pbConfig.GetSpeakingRate())),
		pbConfig.GetVolume(),
		pbConfig.GetMode(),
		pbConfig.GetStream(),
	)

	// 非自研如果透传了version（模型相关），需要变更缓存key
	isAirudder := strings.HasPrefix(strings.ToLower(pbConfig.GetTtsType()), "airudder")
	if !isAirudder &&
		pbConfig.GetVersion() != "" {
		key = strings.TrimSuffix(key, "_v6")
		key = fmt.Sprintf("%s_%s_v6", key, pbConfig.GetVersion())
	}

	// 如果新增了影响语音效果的extra参数，需要变更缓存key
	if !isAirudder &&
		!utils.IsEmptyJsonString(pbConfig.GetExtra()) {
		key = strings.TrimSuffix(key, "_v6")
		key = fmt.Sprintf("%s_%s_v6", key, utils.Md5Join(pbConfig.GetExtra()))
	}

	if version := config.RobotCustomVersion[pbConfig.GetRobotName()]; version != "" {
		key += "_" + version
	}

	return key
}

func NewRealtimeGaugeLabels(pbConfig *pb.Config) prometheus.Labels {
	return prometheus.Labels{
		"tts_type":    pbConfig.TtsType,
		"robot_name":  pbConfig.RobotName,
		"tts_locale":  pbConfig.Locale,
		"tts_speaker": pbConfig.Speaker,
		"company":     pbConfig.Company,
		"stream":      strconv.FormatBool(pbConfig.Stream),
		"server_name": pbConfig.ServerName,
	}
}

func NewAudioFromResponse(responseChan *AudioResponseChan, startTime time.Time, pbConfig *pb.Config) *pb.Audio {
	audio := &pb.Audio{}
	audioResponse, err := responseChan.Response, responseChan.Err

	for _, word := range audioResponse.Words {
		audio.Words = append(audio.Words, &pb.AudioWord{
			Word:     word.Word,
			Duration: word.Duration,
		})
	}

	audio.Timestamp = time.Now().UnixMilli()
	audio.EndTime = audio.Timestamp
	audio.Code = int64(audioResponse.Code)
	audio.Msg = audioResponse.Status
	audio.AudioContent = audioResponse.Content
	audio.GenerateTime = startTime.UnixMilli()
	audio.Duration = audioResponse.Duration
	audio.IsEnd = audioResponse.IsEnd
	audio.TtsKey = pbConfig.GetTtsKey()
	audio.NeedRepeated = code.CheckCodeNeedRepeated(int(audio.Code))
	audio.GeneratedTime = time.Now().UnixMilli()
	audio.DelayTime = audioResponse.DelayTime
	audio.RequestKey = pbConfig.RequestKey
	if err != nil {
		audio.Msg = err.Error()
	}

	return audio
}

func (server *RealtimeTtsGrpcServer) GetCacheResponse(ctx context.Context, pbConfig *pb.Config, uniqueKey string) (*AudioResponse, string, string) {
	// 从缓存获取数据
	var response []byte
	var err error

	cacheVersion := ""
	cacheKey := ""
	if pbConfig.Stream && server.AllowCacheV2(pbConfig) {
		var ssmlTexts []string
		for _, text := range pbConfig.GetText() {
			text = utils.ClearSSMLText(text)
			text = strings.ReplaceAll(text, "  ", " ")
			text = strings.TrimSpace(text)

			ssmlTexts = append(ssmlTexts, text)
		}

		v2CacheKey := fmt.Sprintf(
			"%s_%s_%s_%s",
			strings.ToLower(pbConfig.GetTtsType()),
			strings.ToLower(pbConfig.GetLocale()),
			strings.ToLower(pbConfig.GetSpeaker()),
			strings.Join(ssmlTexts, "+"))

		isAirudder := strings.HasPrefix(strings.ToLower(pbConfig.GetTtsType()), "airudder")
		//如果v2新增了扩展配置
		if !isAirudder && pbConfig.GetVersion() != "" {
			v2CacheKey = fmt.Sprintf("%s_%s", v2CacheKey, pbConfig.GetVersion())
		}
		if !isAirudder && !utils.IsEmptyJsonString(pbConfig.GetExtra()) {
			v2CacheKey = fmt.Sprintf("%s_%s", v2CacheKey, utils.Md5Join(pbConfig.GetExtra()))
		}

		log.Infof(ctx, "v2 cache key: %s", v2CacheKey)
		response, cacheKey, _ = cache.FileClient.GetV2(v2CacheKey)
		cacheVersion = "v2"
	}

	if response == nil && pbConfig.Stream && server.AllowCache(pbConfig) {
		response, cacheKey, err = cache.FileClient.Get(uniqueKey, pbConfig.GetTtsKey())
		cacheVersion = "v1"
	}

	if err != nil {
		log.Errorf(ctx, "cache get error: %s", err.Error())
	}

	// 解析缓存数据
	var cacheResponse *AudioResponse
	if response != nil {
		if err = json.Unmarshal(response, &cacheResponse); err == nil {
			cacheResponse.IsEnd = true
		} else {
			log.Errorf(ctx, "json unmarshal error: %s", err.Error())
		}
	}

	return cacheResponse, cacheKey, cacheVersion
}

func (server *RealtimeTtsGrpcServer) GenerateAudio(ctx context.Context, pbConfig *pb.Config) chan *pb.Audio {
	startTime := time.Now()
	if pbConfig.CallId != "" {
		ctx = context.WithValue(ctx, "call_id", pbConfig.CallId)
	}

	log.Infof(ctx, "请求grpc数据：current_time=%v, %v", time.Now(), server.ConvertPbConfigForData(pbConfig))

	// 使用配置文件中的 grpc server_name 地址，强制替换 grpc 镜像
	metaServerName := config.Config.GetString("server_name")
	if metaServerName != "" && pbConfig.GetTtsV3() {
		pbConfig.ServerName = metaServerName
	}

	// todo callid维度并发几率低,且DM顺序执行等待。暂不singlefly，理论上11labs multi_context需要callid维度保证顺序
	uniqueKey := server.getUniqueKey(pbConfig)
	audioChan := make(chan *pb.Audio, 0)
	go func() {
		defer func() {
			metrics.RealtimeGauge.With(NewRealtimeGaugeLabels(pbConfig)).Dec()
			close(audioChan)
		}()

		metrics.RealtimeGauge.With(NewRealtimeGaugeLabels(pbConfig)).Inc()

		var responseChannels chan AudioResponseChan
		var useCache = false
		cacheResponse, cacheKey, cacheVersion := server.GetCacheResponse(ctx, pbConfig, uniqueKey)
		if cacheResponse != nil {
			useCache = true
			responseChannels = make(chan AudioResponseChan, 1)
			responseChannels <- AudioResponseChan{Response: cacheResponse}
		}

		firstTime := time.Now()
		allDuration := float32(0)
		if !useCache {
			responseChannels = GenerateAudio(ctx, pbConfig)
			cacheVersion = ""
		}

		contents := make([][]byte, 0)
		for responseChan := range responseChannels {
			audioResponse := responseChan.Response

			audio := NewAudioFromResponse(&responseChan, startTime, pbConfig)
			audio.UseCache = useCache
			audio.CacheVersion = cacheVersion
			audio.CacheKey = cacheKey

			audioChan <- audio

			contents = append(contents, audio.AudioContent)
			allDuration += audio.Duration / 1e3

			if (audio.Code == code.ServerOK || audio.Code == 0) && !useCache {
				diff := float32(time.Now().Sub(firstTime).Milliseconds())
				if diff > allDuration {
					metrics.DurationObserve(
						metrics.RealtimeDelayFrameDuration, metrics.RealtimeDelayFrameBucket, pbConfig.RobotName, pbConfig.TtsType, pbConfig.Locale,
						pbConfig.Speaker, pbConfig.Company, pbConfig.ServerName, pbConfig.Stream, float64(diff-allDuration))
				}
			}

			if audioResponse.IsEnd {
				responseStr := metrics.ResponseV3Error
				if audio.Code == code.ServerOK || audio.Code == 0 {
					if useCache {
						responseStr = metrics.ResponseV3Cache
					} else {
						responseStr = metrics.ResponseV3Success
						if pbConfig.Stream {
							audioResponse.Content = bytes.Join(contents, nil)
							audioResponse.Duration = allDuration * 1e3
							cache.FileClient.Set(uniqueKey, pbConfig.GetTtsKey(), pbConfig.CallId, audioResponse)
						}
					}
				}

				metrics.RequestObserve(pbConfig.RobotName, pbConfig.TtsType, pbConfig.Locale,
					pbConfig.Speaker, pbConfig.Company, pbConfig.ServerName, responseStr, int(audio.Code), pbConfig.Stream, startTime)
				log.Infof(ctx, "%s 请求完成, 命中缓存：%s, code=%d, status=%s, frames=%d, diffTIme=%d",
					uniqueKey, useCache, audio.Code, audio.Msg, len(contents), time.Now().Sub(startTime).Milliseconds())
			}

			if useCache {
				close(responseChannels)
			}
		}
	}()

	return audioChan
}

func (server *RealtimeTtsGrpcServer) GenerateDoubleStreamAudio(ctx context.Context, pbConfig *pb.Config, startTime time.Time, needCache bool) (chan string, chan *pb.Audio) {
	if pbConfig.CallId != "" {
		ctx = context.WithValue(ctx, "call_id", pbConfig.CallId)
	}

	log.Infof(ctx, "请求grpc双流数据：current_time=%v, %v, %v", time.Now(), server.ConvertPbConfigForData(pbConfig), needCache)
	// 使用配置文件中的 grpc server_name 地址，强制替换 grpc 镜像
	metaServerName := config.Config.GetString("server_name")
	if metaServerName != "" && pbConfig.GetTtsV3() {
		pbConfig.ServerName = metaServerName
	}

	uniqueKey := server.getUniqueKey(pbConfig)
	if needCache {
		cacheResponse, cacheKey, cacheVersion := server.GetCacheResponse(ctx, pbConfig, uniqueKey)

		if cacheResponse != nil {
			responseChains := make(chan *pb.Audio, 1)
			audio := NewAudioFromResponse(&AudioResponseChan{Response: cacheResponse}, startTime, pbConfig)
			audio.UseCache = true
			audio.CacheKey = cacheKey
			audio.CacheVersion = cacheVersion

			responseChains <- audio
			close(responseChains)

			return make(chan string, 10), responseChains
		}
	}

	textChan, responseChannels := GenerateDoubleStreamAudio(ctx, pbConfig)
	audioChan := make(chan *pb.Audio, 10)
	go func() {
		defer func() {
			close(audioChan)
		}()

		contents := make([][]byte, 0)
		allDuration := float32(0)
		for responseChan := range responseChannels {
			audioResponse := responseChan.Response
			audio := NewAudioFromResponse(&responseChan, startTime, pbConfig)
			audioChan <- audio

			contents = append(contents, audio.AudioContent)
			allDuration += audio.Duration / 1e3

			if needCache && audioResponse.IsEnd && (audio.Code == code.ServerOK || audio.Code == 0) {
				audioResponse.Content = bytes.Join(contents, nil)
				audioResponse.Duration = allDuration * 1e3
				cache.FileClient.Set(uniqueKey, pbConfig.GetTtsKey(), pbConfig.CallId, audioResponse)
			}
		}
	}()

	return textChan, audioChan
}

func (server *RealtimeTtsGrpcServer) Generate(ctx context.Context, pbConfig *pb.Config) (*pb.Audio, error) {
	for audio := range server.GenerateAudio(ctx, pbConfig) {
		return audio, nil
	}

	return nil, errors.New("generate Audio Error")
}

func (server *RealtimeTtsGrpcServer) GenerateWithStream(stream pb.RealtimeTTS_GenerateWithStreamServer) error {
	StreamCount.Inc()
	defer func() {
		StreamCount.Dec()
	}()

	cancelMaps := make(map[string]context.CancelFunc)
	defer func() {
		for _, cancelFunc := range cancelMaps {
			cancelFunc()
		}
	}()

	for {
		pbConfig, err := stream.Recv()
		if err == io.EOF {
			return nil
		}

		if err != nil {
			log.Errorf(stream.Context(), "grpc stream error: %s", err.Error())
			return err
		}

		switch pbConfig.CommandType {
		case pb.Config_Cancel:
			//todo zb 如果是11labs，需要用context_id取消
			for _, requestKey := range pbConfig.CancelRequestKeys {
				if cancelFunc, ok := cancelMaps[requestKey]; ok {
					log.Infof(stream.Context(), "cancel request key: %s", requestKey)
					cancelFunc()
				}
			}
		default:
			ctx, cancelFunc := context.WithCancel(stream.Context())
			cancelMaps[pbConfig.RequestKey] = cancelFunc
			go func() {
				for audio := range server.GenerateAudio(ctx, pbConfig) {
					if err := stream.Send(audio); err != nil {
						log.Errorf(ctx, "发送 grpc 消息失败：%s", err.Error())
					}
				}
			}()
		}
	}
}

func (server *RealtimeTtsGrpcServer) GenerateWithDoubleStream(stream pb.RealtimeTTS_GenerateWithDoubleStreamServer) error {
	StreamCount.Inc()
	defer func() {
		StreamCount.Dec()
	}()

	cancelMaps := make(map[string]context.CancelFunc)
	contentMaps := make(map[string]chan string)
	defer func() {
		for _, cancelFunc := range cancelMaps {
			cancelFunc()
		}
	}()

	for {
		ds, err := stream.Recv()
		if err == io.EOF {
			return nil
		}

		if err != nil {
			log.Errorf(stream.Context(), "grpc stream error: %s", err.Error())
			return err
		}

		switch ds.Request.(type) {
		case *pb.DoubleStreamConfig_Config:
			pbConfig := ds.GetConfig()
			switch pbConfig.CommandType {
			case pb.Config_Cancel:
				for _, requestKey := range pbConfig.CancelRequestKeys {
					if cancelFunc, ok := cancelMaps[requestKey]; ok {
						log.Infof(stream.Context(), "cancel request key: %s", requestKey)
						cancelFunc()
					}
				}
			default:
				startTime := time.Now()

				ctx, cancelFunc := context.WithCancel(stream.Context())
				cancelMaps[ds.RequestKey] = cancelFunc

				contentC, audioC := server.GenerateDoubleStreamAudio(ctx, pbConfig, startTime, ds.NeedCache)
				if ds.NeedCache {
					contentC <- pbConfig.Text[0]
					close(contentC)
				} else {
					contentMaps[ds.RequestKey] = contentC
				}

				log.Infof(ctx, "request key: %s", ds.RequestKey)
				go func() {
					defer func() {
						metrics.RealtimeGauge.With(NewRealtimeGaugeLabels(pbConfig)).Dec()
					}()

					metrics.RealtimeGauge.With(NewRealtimeGaugeLabels(pbConfig)).Inc()
					for audio := range audioC {
						if err := stream.Send(audio); err != nil {
							log.Errorf(ctx, "发送 grpc 消息失败：%s", err.Error())
						}
					}
				}()
			}

		case *pb.DoubleStreamConfig_Content:
			log.Infof(context.Background(), "send request key: %s", ds.RequestKey)
			content := ds.Request.(*pb.DoubleStreamConfig_Content).Content
			textChan, ok := contentMaps[ds.RequestKey]
			if !ok {
				log.Errorf(stream.Context(), "request key not found: %s", ds.RequestKey)
				continue
			}
			if content.Text != "" {
				textChan <- content.Text
			}

			if content.IsEnd {
				close(textChan)
				delete(contentMaps, ds.RequestKey)
			}

			log.Infof(context.Background(), "send request key done: %s", ds.RequestKey)
		}
	}
}
