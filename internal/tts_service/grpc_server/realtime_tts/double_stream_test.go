package realtime_tts

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"
	"tts-service/internal/pkg/log"
	"tts-service/rpc/service/pb"
)

func TestGenerateDoubleStreamAudio(t *testing.T) {
	log.InitLogger()

	pbConfg := &pb.Config{
		Timestamp: 0,
		Speaker:   "21m00Tcm4TlvDq8ikWAM",
		TtsType:   "elevenlabs__v1",
		Locale:    "eleven_monolingual_v1",
		CallId:    "-------11111111",
		Text:      []string{""},
		Stream:    true,
	}

	server := &RealtimeTtsGrpcServer{}
	contentC, audioC := server.GenerateDoubleStreamAudio(context.Background(), pbConfg, time.Now(), false)
	go func() {
		texts := "Hey, this is Customer Service calling back on your pest control request a couple of minutes ago. Sorry, we missed you. Please give us a call back at (559)-550-5353 to discuss your pest control needs."
		s := strings.Split(texts, " ")
		for _, text := range s {
			contentC <- fmt.Sprintf("%s ", text)

			time.Sleep(10 * time.Millisecond)
		}

		close(contentC)
	}()

	for audio := range audioC {
		fmt.Printf("duration=%f, msg=%s\n", audio.Duration)
	}

}
