package tts_service

import (
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
	"tts-service/internal/pkg/log"
	"tts-service/internal/pkg/middleware"
)

type httpServer struct {
	ctx        context.Context
	httpServer *http.Server
	ctxCancel  context.CancelFunc
}

// 返回 http 服务
func newHttpServer(ctx context.Context, port int) *httpServer {
	cancelCtx, cancel := context.WithCancel(ctx)
	server := httpServer{
		ctx:       cancelCtx,
		ctxCancel: cancel,
		httpServer: &http.Server{
			Addr:         fmt.Sprintf(":%d", port),
			ReadTimeout:  120 * time.Second,
			WriteTimeout: 120 * time.Second,
		},
	}

	app := gin.New()
	server.initMiddleWare(app)
	server.initHandler(app)
	server.httpServer.Handler = app
	return &server
}

func (server *httpServer) initMiddleWare(app *gin.Engine) {
	app.Use(gin.Recovery())
	app.Use(middleware.CorsMiddleware)
	//app.Use(middleware.LogRequestUrl)
	app.NoRoute(func(c *gin.Context) {
		c.JSON(http.StatusNotFound, gin.H{"code": "PAGE_NOT_FOUND", "message": "404 page not found"})
	})

	app.NoMethod(func(c *gin.Context) {
		c.JSON(http.StatusMethodNotAllowed, gin.H{"code": "METHOD_NOT_ALLOWED", "message": "405 method not allowed"})
	})
}

func (server *httpServer) initHandler(app *gin.Engine) {
	InitRoute(app)
}

func (server *httpServer) start() {
	go func(s *httpServer) {
		if err := s.httpServer.ListenAndServe(); err != nil {
			log.Errorf(server.ctx, "http error: %s", err.Error())
			return
		}
	}(server)
}

func (server *httpServer) stop() {
	go func() {
		time.Sleep(2 * time.Second)
		server.ctxCancel()
	}()

	if err := server.httpServer.Shutdown(server.ctx); err != nil {
		fmt.Println(err.Error())
		return
	}
}
