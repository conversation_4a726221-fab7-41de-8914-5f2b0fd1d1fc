package models

import (
	"fmt"
	"github.com/pkg/errors"
	"strings"
	"tts-service/internal/pkg/constants"
)

const (
	GrpcClient = "grpc"
)

type TTSRequest struct {
	Version       string  `form:"version" json:"version,omitempty"`
	TTSType       string  `form:"tts_type" json:"tts_type,omitempty"`
	Speaker       string  `form:"speaker" json:"speaker,omitempty"`
	VoiceID       string  `form:"voice_id" json:"voice_id,omitempty"`
	Locale        string  `form:"locale" json:"locale,omitempty"`
	Text          string  `form:"text" json:"text,omitempty"`
	AccountType   string  `form:"account_type" json:"account_type,omitempty"`
	ServerName    string  `form:"server_name" json:"server_name,omitempty"`
	AudioEncoding string  `form:"audio_encoding" json:"audio_encoding,omitempty"`
	SampleRate    float64 `form:"sample_rate" json:"sample_rate,omitempty"`
	VolumeGainDb  float64 `form:"volume_gain_db" json:"volume_gain_db,omitempty"`
	SpeakingRate  float64 `form:"speaking_rate" json:"speaking_rate,omitempty"`
	Extra         string  `form:"extra" json:"extra,omitempty"`

	// 机器人通话数据
	CallId      string `form:"callid" json:"call_id,omitempty"`
	RobotName   string `form:"robotName" json:"robotName,omitempty"`
	Company     string `form:"company" json:"company,omitempty"`
	PhoneNumber string `form:"phoneNumber" json:"phone_number,omitempty"`
	Mode        string `form:"mode" json:"mode,omitempty"`
	AppName     string `form:"app_name" json:"app_name,omitempty"`
	Stream      bool   `form:"stream" json:"stream,omitempty"`
	RequestKey  string `form:"request_key" json:"request_key,omitempty"`
	StateID     string `form:"state_id" json:"state_id,omitempty"`

	// 请求访问方式
	Client string `form:"client" json:"client,omitempty"`

	Texts []string `json:"-"`
}

func (request *TTSRequest) ConvertSpeed(provider string) float64 {
	switch provider {
	case constants.XunfeiProvider:
		//讯飞语速 0-100，默认50
		if request.SpeakingRate == 0 || request.SpeakingRate == 1 {
			return 50
		}
		rate := request.SpeakingRate * 10
		if rate > 100 || rate < 0 {
			return 50
		}
		return rate

	default:
		return request.SpeakingRate

	}
}

func (request *TTSRequest) ConvertVolume(provider string) float64 {
	switch provider {
	case constants.XunfeiProvider:
		if request.VolumeGainDb == 0 {
			return 50
		}
		volume := request.VolumeGainDb * 10
		if volume > 100 || volume < 0 {
			return 50
		}
		return volume
	default:
		return request.VolumeGainDb

	}
}

func (request *TTSRequest) ToAirudderV1GetUrl() string {
	urls := make([]string, 0)
	urls = append(urls, "tts_type=airudder__v1")
	if request.Speaker != "" {
		urls = append(urls, fmt.Sprintf("speaker=%s", request.Speaker))
	}

	if request.Locale != "" {
		urls = append(urls, fmt.Sprintf("locale=%s", request.Locale))
	}

	if request.Text != "" {
		urls = append(urls, fmt.Sprintf("text=%s", request.Text))
	}

	return strings.Join(urls, "&")
}

func (request *TTSRequest) GetDefaultSampleRate() int {
	if request.SampleRate != 0 {
		return int(request.SampleRate)
	}

	return 8000
}

func (request *TTSRequest) UseGrpc() bool {
	return request.Client == GrpcClient
}

func (request *TTSRequest) IsEmpty() bool {
	return request.Speaker == "" && request.TTSType == ""
}

// GetProvider 根据参数返回 tts 引擎
func (request *TTSRequest) GetProvider() (error, string) {
	if request.TTSType == "" {
		if strings.HasPrefix(strings.ToLower(request.Speaker), constants.AirudderProviderPrefix) {
			return nil, constants.AirudderV1Provider
		}

		return nil, constants.GoogleProvider
	}

	providers := strings.Split(request.TTSType, constants.ProviderSplit)
	if len(providers) < 1 {
		return errors.New("switch tts_type error"), ""
	}

	if len(providers) == 1 {
		return nil, providers[0]
	}

	if providers[0] == constants.AirudderProviderPrefix {
		if providers[1] == constants.AirudderV1ProviderSuffix {
			return nil, constants.AirudderV1Provider
		}

		if providers[1] == constants.AirudderV2ProviderSuffix {
			return nil, constants.AirudderV2Provider
		}

		return errors.New("airudder not support version"), ""
	}

	switch providers[0] {
	case constants.GoogleProvider:
		return nil, constants.GoogleProvider
	case constants.MicrosoftProvider:
		return nil, constants.MicrosoftProvider
	case constants.ElevenLabsProvider:
		return nil, constants.ElevenLabsProvider
	case constants.OpenAiProvider:
		return nil, constants.OpenAiProvider
	case constants.XunfeiProvider:
		return nil, constants.XunfeiProvider
	case constants.MinimaxProvider:
		return nil, constants.MinimaxProvider
	case constants.DeepgramProvider:
		return nil, constants.DeepgramProvider
	case constants.AliyunProvider:
		return nil, constants.AliyunProvider
	default:
		return errors.New("tts_type is not support"), ""
	}
}

func (request *TTSRequest) IsElevenlabsV2() bool {
	if request.TTSType == "" {
		return false
	}

	providers := strings.Split(request.TTSType, constants.ProviderSplit)
	if len(providers) < 2 {
		return false
	}

	return providers[0] == constants.ElevenLabsProvider && providers[1] == constants.AirudderV2ProviderSuffix
}
