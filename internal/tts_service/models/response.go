package models

type TTSResponse struct {
	Data         interface{} `json:"data"`
	Status       string      `json:"status"`
	Code         int         `json:"code"`
	NeedRepeated bool        `json:"need_repeated"`
}

type ErrorResponse struct {
	Status string `json:"status"`
	Code   int    `json:"code"`
}

func NewErrorResponse(code int, status string) *ErrorResponse {
	return &ErrorResponse{
		Status: status,
		Code:   code,
	}
}
