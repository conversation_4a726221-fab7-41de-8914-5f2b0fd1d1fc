package models

import (
	"archive/zip"
	"fmt"
	"mime/multipart"
	"strings"
	"tts-service/internal/pkg/constants"
)

type CacheAudioParams struct {
	TTSType   string                `json:"tts_type" form:"tts_type"`
	Locale    string                `json:"locale" form:"locale"`
	Speaker   string                `json:"speaker" form:"speaker"`
	Text      string                `json:"text" form:"text"`
	AudioFile *multipart.FileHeader `json:"audio_file" form:"audio_file"`
}

func (params *CacheAudioParams) GetTTSType() string {
	if strings.Contains(params.TTSType, constants.ProviderSplit) {
		return params.TTSType
	}

	switch params.TTSType {
	case constants.AirudderProviderPrefix:
		return fmt.Sprintf("%s%s%s", params.TTSType, constants.ProviderSplit, constants.AirudderV2ProviderSuffix)
	default:
		return fmt.Sprintf("%s%s%s", params.TTSType, constants.ProviderSplit, constants.AirudderV1ProviderSuffix)
	}
}

type CacheBatchAudioParams struct {
	Region       string                `json:"region" form:"region"`
	CustomRegion []string              `json:"custom_region" form:"custom_region"`
	AudioFile    *multipart.FileHeader `json:"audio_file" form:"audio_file"`
	CsvFile      *multipart.FileHeader `json:"csv_file" form:"csv_file"`
}

func (params *CacheBatchAudioParams) GetRegions() []string {
	if params.Region == "all" {
		return []string{"all"}
	}

	return params.CustomRegion
}

type CacheAudio struct {
	TTSType string
	Locale  string
	Speaker string
	Text    string
	Audio   *zip.File
}
