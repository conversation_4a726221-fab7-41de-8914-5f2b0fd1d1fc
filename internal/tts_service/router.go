package tts_service

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
	"tts-service/internal/pkg/config"
	"tts-service/internal/pkg/metrics"
	"tts-service/internal/tts_service/controller"
)

func InitRoute(app *gin.Engine) {
	gin.SetMode(gin.ReleaseMode)

	time.Sleep(5 * time.Second)
	app.GET("/metrics", metrics.PrometheusHandler())

	app.StaticFS("/audio", http.Dir("audio"))

	app.GET("/tts", controller.TTSController)
	app.POST("/tts", controller.TTSController)
	app.GET("/tts_test", controller.TTSTestController)
	app.POST("/tts_test", controller.TTSTestController)

	app.POST("/cache_delete", controller.TTSCacheDelete)
	app.PUT("/cache_reload", controller.TTSCacheReload)

	app.POST("/cache/add", controller.TTSCacheAdd)

	app.StaticFile("/cache/cache_file", "html/template/cache.csv")

	app.LoadHTMLGlob("/tts-service/html/*.html")
	app.GET("/cache", func(ctx *gin.Context) {
		ctx.HTML(http.StatusOK, "upload.html", gin.H{
			"Address": config.CacheV2Address,
		})
	})

	app.POST("/cache/batch_add", controller.TTSCacheBatchAdd)
}
